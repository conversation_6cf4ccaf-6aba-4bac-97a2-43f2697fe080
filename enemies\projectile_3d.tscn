[gd_scene load_steps=6 format=3 uid="uid://y1wfaam1ipgh"]

[ext_resource type="Script" uid="uid://bpokn3i5paoyn" path="res://enemies/projectile_3d.gd" id="1_script"]

[sub_resource type="SphereShape3D" id="SphereShape3D_1"]
radius = 0.1

[sub_resource type="SphereMesh" id="SphereMesh_1"]
radius = 0.1
height = 0.2

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_1"]
albedo_color = Color(1, 0.5, 0, 1)
emission_enabled = true
emission = Color(1, 0.3, 0, 1)

[sub_resource type="ParticleProcessMaterial" id="ParticleProcessMaterial_1"]
initial_velocity_min = 2.0
initial_velocity_max = 5.0
gravity = Vector3(0, 0, 0)
scale_min = 0.1
scale_max = 0.3

[node name="Projectile3D" type="RigidBody3D"]
collision_layer = 8
gravity_scale = 0.0
script = ExtResource("1_script")

[node name="CollisionShape3D" type="CollisionShape3D" parent="."]
shape = SubResource("SphereShape3D_1")

[node name="MeshInstance3D" type="MeshInstance3D" parent="."]
mesh = SubResource("SphereMesh_1")
surface_material_override/0 = SubResource("StandardMaterial3D_1")

[node name="GPUParticles3D" type="GPUParticles3D" parent="."]
process_material = SubResource("ParticleProcessMaterial_1")
