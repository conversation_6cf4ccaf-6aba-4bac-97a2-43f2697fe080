# Nextbot AI System Analysis
## Core Logic Documentation for Godot Implementation

This document analyzes the Nextbot AI system from the Source SDK to extract the core logic patterns for implementing enemy AI in Godot.

## Overview

The Nextbot system uses a sophisticated **Component-Based Architecture** with an **Action-Based Behavior Engine**. The system is built around an "Actor" that coordinates multiple specialized components.

### Core Architecture Components

#### **Actor (Main Controller)**
The central entity that coordinates all components and responds to events. When an event occurs (like `OnInjured`), the Actor responds by changing the behavior of its components.

#### **Component System**
Each NextBot has four main components that handle different aspects of AI:

1. **Locomotion** - Handles movement through the environment
   - Pathfinding and navigation
   - Movement speed and acceleration
   - Obstacle avoidance
   - Example: When injured, locomotion handles fleeing to a safe position

2. **Body** - Handles animations and physical representation
   - Animation playback and transitions
   - Posture and stance
   - Physical interactions
   - Example: When injured, body plays flinching animation

3. **Vision** - Handles perception and awareness (Optional)
   - Field-of-view calculations
   - Line-of-sight detection
   - Entity recognition
   - Example: Detecting enemies within view cone
   - Note: Not required - some bots (like TF2 skeletons) work without vision

4. **Intention** - Manages behavior selection and transitions
   - Behavior decision making
   - Priority management
   - State transitions
   - Example: Choosing to flee when injured

### Behavior Hierarchy

The system uses a hierarchical structure:

- **Intention** → **Behavior** → **Action**
- **Behavior**: Contains a series of Actions (equivalent to NPC "Schedule")
- **Action**: Contains the actual code to execute (equivalent to NPC "Task")
- **Child Actions**: Actions can have sub-actions that run simultaneously

### Key Concepts

- **Actions**: Individual behaviors that can be executed, suspended, and resumed
- **Action Stacking**: Actions can interrupt and suspend other actions, creating a hierarchy
- **Event-Driven**: Actions respond to game events (damage, sight, sound, etc.)
- **Result-Based Transitions**: Actions return results that determine state changes

## Core AI States

### 1. **DEAD State**
**Purpose**: Handle bot when it's dead or dying

**Conditions to Enter**:
- Bot's health reaches 0 or below
- Bot receives fatal damage
- Bot is in dying animation

**Tasks/Schedules**:
- Play death animation
- Wait for respawn timer
- Handle death events (drop items, notify team, etc.)

**Conditions to Exit**:
- Respawn timer expires
- Bot is revived by medic
- Round restart

---

### 2. **TACTICAL MONITOR State**
**Purpose**: High-level decision making and priority management

**Conditions to Enter**:
- Bot is alive and active
- Main behavior system is running

**Tasks/Schedules**:
- **Health Monitoring**: Check if health is low enough to seek health
- **Ammo Monitoring**: Check if ammo is low enough to seek ammo
- **Threat Assessment**: Evaluate current threats and danger levels
- **Retreat Evaluation**: Determine if bot should retreat
- **Teleporter Usage**: Check for nearby teleporters
- **Sticky Bomb Management**: Monitor and detonate sticky bombs
- **Enemy Avoidance**: Avoid bumping into enemies

**Priority Interruptions**:
- **Low Health**: Interrupt current action to seek health
- **Low Ammo**: Interrupt current action to seek ammo
- **Critical Threat**: Interrupt to retreat or take cover
- **Round End**: Handle victory/defeat conditions

**Conditions to Exit**:
- Bot dies
- Bot becomes inactive

---

### 3. **SEEK AND DESTROY State**
**Purpose**: Actively hunt and attack enemies

**Conditions to Enter**:
- Enemy is visible or recently seen
- Bot is healthy enough to engage
- Bot has sufficient ammo
- No higher priority needs (health, ammo, retreat)

**Tasks/Schedules**:
- **Target Selection**: Choose the most dangerous/threatening enemy
- **Pathfinding**: Compute path to target
- **Movement**: Move toward target using path following
- **Weapon Management**: Select appropriate weapon for target
- **Combat**: Attack when in range
- **Cover Usage**: Use cover while approaching
- **Target Switching**: Switch targets if current target becomes invalid

**Sub-Behaviors**:
- **Chase Path**: Dynamic path that updates to follow moving targets
- **Attack**: Combat behavior when in range
- **Cover Seeking**: Find cover while approaching

**Conditions to Exit**:
- Target is killed or lost
- Bot health becomes too low
- Bot runs out of ammo
- Higher priority action needed (retreat, health, ammo)
- Bot gets stuck or path fails

---

### 4. **ATTACK State**
**Purpose**: Engage in direct combat with a specific target

**Conditions to Enter**:
- Target is within attack range
- Line of sight is clear
- Bot has appropriate weapon equipped

**Tasks/Schedules**:
- **Aiming**: Aim at target with appropriate lead/compensation
- **Weapon Firing**: Fire weapon when conditions are met
- **Movement**: Strafe and dodge while attacking
- **Cover Usage**: Use cover between attacks
- **Target Tracking**: Keep track of moving targets

**Weapon-Specific Behaviors**:
- **Hitscan Weapons**: Instant aim and fire
- **Projectile Weapons**: Lead targets and account for travel time
- **Explosive Weapons**: Use splash damage and area denial
- **Melee Weapons**: Close distance and attack

**Conditions to Exit**:
- Target is killed
- Target moves out of range
- Line of sight is broken
- Bot health becomes too low
- Bot runs out of ammo

---

### 5. **RETREAT TO COVER State**
**Purpose**: Move to safe location when threatened

**Conditions to Enter**:
- Bot health is critically low
- Bot is outnumbered
- Bot is under heavy fire
- Bot has no ammo
- Bot is being flanked

**Tasks/Schedules**:
- **Cover Assessment**: Find nearby safe cover areas
- **Pathfinding**: Compute path to cover
- **Movement**: Move to cover as quickly as possible
- **Threat Avoidance**: Avoid enemies while retreating
- **Cover Usage**: Stay in cover until safe

**Cover Types**:
- **Physical Cover**: Walls, obstacles, buildings
- **Distance Cover**: Move away from threats
- **Team Cover**: Move toward teammates
- **Health/Ammo Cover**: Move toward supplies

**Conditions to Exit**:
- Bot reaches safe cover and threat is gone
- Bot health is restored
- Bot gets ammo
- Teammates arrive to help
- Bot dies

---

### 6. **GET HEALTH State**
**Purpose**: Seek and collect health supplies

**Conditions to Enter**:
- Bot health is below threshold (typically 50-70%)
- Health kit or dispenser is available
- No immediate threats

**Tasks/Schedules**:
- **Health Source Detection**: Find nearest health kit or dispenser
- **Pathfinding**: Compute path to health source
- **Movement**: Move to health source
- **Collection**: Pick up health when in range
- **Threat Monitoring**: Watch for enemies while moving

**Health Sources**:
- **Health Kits**: Static health pickups
- **Dispensers**: Engineer-built health sources
- **Medics**: Teammate medics
- **Health Packs**: Dropped health items

**Conditions to Exit**:
- Bot collects health
- Health source becomes unavailable
- Immediate threat appears
- Bot dies

---

### 7. **GET AMMO State**
**Purpose**: Seek and collect ammunition

**Conditions to Enter**:
- Bot ammo is below threshold
- Ammo source is available
- No immediate threats

**Tasks/Schedules**:
- **Ammo Source Detection**: Find nearest ammo source
- **Pathfinding**: Compute path to ammo source
- **Movement**: Move to ammo source
- **Collection**: Pick up ammo when in range
- **Threat Monitoring**: Watch for enemies while moving

**Ammo Sources**:
- **Ammo Boxes**: Static ammo pickups
- **Dispensers**: Engineer-built ammo sources
- **Resupply Lockers**: Spawn room resupply
- **Dropped Ammo**: Ammo from killed enemies

**Conditions to Exit**:
- Bot collects ammo
- Ammo source becomes unavailable
- Immediate threat appears
- Bot dies

---

### 8. **PATROL State**
**Purpose**: Move between patrol points when no threats are present

**Conditions to Enter**:
- No enemies visible
- Bot is healthy and has ammo
- No objectives requiring attention

**Tasks/Schedules**:
- **Patrol Point Selection**: Choose next patrol point
- **Pathfinding**: Compute path to patrol point
- **Movement**: Move to patrol point
- **Area Scanning**: Look for enemies while patrolling
- **Patrol Point Reached**: Wait briefly at patrol point

**Patrol Behaviors**:
- **Random Patrol**: Choose random patrol points
- **Sequential Patrol**: Follow predefined patrol route
- **Area Patrol**: Patrol within specific area
- **Objective Patrol**: Patrol near important objectives

**Conditions to Exit**:
- Enemy is spotted
- Bot takes damage
- Objective becomes available
- Bot receives orders

---

### 9. **IDLE State**
**Purpose**: Wait and observe when no action is needed

**Conditions to Enter**:
- No enemies visible
- No objectives requiring attention
- Bot is in safe location
- Bot is waiting for something

**Tasks/Schedules**:
- **Environment Scanning**: Look around for threats or opportunities
- **Animation**: Play idle animations
- **Sound Monitoring**: Listen for enemy sounds
- **Team Coordination**: Communicate with teammates

**Conditions to Exit**:
- Enemy is spotted
- Bot takes damage
- Objective becomes available
- Bot receives orders

---

## State Transition Logic

### Priority-Based Interruptions
The system uses a priority system where higher priority states can interrupt lower priority ones:

1. **DEAD** (Highest Priority)
2. **TACTICAL MONITOR** (High Priority - can interrupt most actions)
3. **RETREAT TO COVER** (High Priority - survival)
4. **GET HEALTH** (Medium-High Priority)
5. **GET AMMO** (Medium Priority)
6. **SEEK AND DESTROY** (Medium Priority)
7. **ATTACK** (Medium Priority)
8. **PATROL** (Low Priority)
9. **IDLE** (Lowest Priority)

### Event-Driven Transitions
States can transition based on events:

- **OnSight**: Enemy becomes visible
- **OnLostSight**: Enemy is no longer visible
- **OnInjured**: Bot takes damage
- **OnKilled**: Bot dies
- **OnOtherKilled**: Teammate or enemy dies
- **OnSound**: Bot hears a sound
- **OnContact**: Bot touches something
- **OnStuck**: Bot gets stuck while moving
- **OnMoveToSuccess**: Bot reaches destination
- **OnMoveToFailure**: Bot fails to reach destination

### Condition-Based Transitions
States transition based on conditions:

- **Health Thresholds**: Low health triggers health-seeking
- **Ammo Thresholds**: Low ammo triggers ammo-seeking
- **Threat Assessment**: Danger level determines retreat
- **Distance Checks**: Target too far/close
- **Line of Sight**: Can/cannot see target
- **Team Status**: Teammates nearby or in trouble

## Implementation Notes for Godot

### Component-Based Architecture
```gdscript
# Main Actor class
class_name NextBot
extends CharacterBody3D

var locomotion: LocomotionComponent
var body: BodyComponent  
var vision: VisionComponent
var intention: IntentionComponent

func _ready():
    locomotion = LocomotionComponent.new()
    body = BodyComponent.new()
    vision = VisionComponent.new()
    intention = IntentionComponent.new()
    
    add_child(locomotion)
    add_child(body)
    add_child(vision)
    add_child(intention)

# Event handling - Actor coordinates components
func on_injured(damage_info: Dictionary):
    body.play_flinch_animation()
    intention.evaluate_retreat_need()
    locomotion.prepare_retreat_movement()
```

### Component Interfaces
```gdscript
# Base component class
class_name BotComponent
extends Node

var bot: NextBot

func _init(bot_ref: NextBot):
    bot = bot_ref

# Locomotion Component
class_name LocomotionComponent
extends BotComponent

func move_to(target_position: Vector3):
    # Handle pathfinding and movement
    pass

func approach(target: Node):
    # Move toward target with obstacle avoidance
    pass

# Body Component  
class_name BodyComponent
extends BotComponent

func play_animation(anim_name: String):
    # Handle animation playback
    pass

func set_posture(posture: String):
    # Handle stance and posture
    pass

# Vision Component
class_name VisionComponent
extends BotComponent

func can_see(target: Node) -> bool:
    # Check line of sight and field of view
    pass

func get_visible_enemies() -> Array:
    # Return list of visible enemies
    pass

# Intention Component
class_name IntentionComponent
extends BotComponent

var current_behavior: Behavior

func select_behavior() -> Behavior:
    # Choose appropriate behavior based on conditions
    pass

func evaluate_retreat_need() -> bool:
    # Determine if bot should retreat
    pass
```

### State Machine Structure
```gdscript
# Base Action class
class_name Action
extends Node

var bot: Bot
var is_active: bool = false

func on_start() -> ActionResult:
    pass

func update(delta: float) -> ActionResult:
    pass

func on_suspend() -> ActionResult:
    pass

func on_resume() -> ActionResult:
    pass

# Action results
enum ActionResult {
    CONTINUE,
    CHANGE_TO,
    SUSPEND_FOR,
    DONE
}
```

### Event System
```gdscript
# Event handling
func on_sight(enemy: Node) -> ActionResult:
    pass

func on_injured(damage_info: Dictionary) -> ActionResult:
    pass

func on_stuck() -> ActionResult:
    pass
```

### Priority Management
```gdscript
# Priority-based action selection
func select_action() -> Action:
    if bot.is_dead():
        return DeadAction.new()
    elif bot.health < bot.health_threshold:
        return GetHealthAction.new()
    elif bot.ammo < bot.ammo_threshold:
        return GetAmmoAction.new()
    elif bot.has_threats():
        return SeekAndDestroyAction.new()
    else:
        return PatrolAction.new()

# Intention component handles behavior selection
class IntentionComponent:
    func select_behavior() -> Behavior:
        # Check priorities in order
        if should_retreat():
            return RetreatBehavior.new()
        elif needs_health():
            return GetHealthBehavior.new()
        elif needs_ammo():
            return GetAmmoBehavior.new()
        elif has_enemies():
            return SeekAndDestroyBehavior.new()
        else:
            return PatrolBehavior.new()
```

### Pathfinding Integration
```gdscript
# Path following
var path_follower: PathFollower

func update_movement(delta: float):
    if path_follower.has_path():
        var next_point = path_follower.get_next_point()
        bot.move_toward(next_point)

# Locomotion component handles pathfinding
class LocomotionComponent:
    var navigation_agent: NavigationAgent3D
    
    func move_to(target_position: Vector3):
        navigation_agent.target_position = target_position
        
    func approach(target: Node):
        if target:
            move_to(target.global_position)
            
    func update_movement(delta: float):
        if navigation_agent.is_navigation_finished():
            return
            
        var next_position = navigation_agent.get_next_path_position()
        var direction = (next_position - bot.global_position).normalized()
        bot.velocity = direction * bot.speed
```

## Event Flow Example

Here's how the component system responds to an event like `OnInjured`:

1. **Event Occurs**: Bot takes damage
2. **Actor Response**: NextBot receives `on_injured()` call
3. **Component Coordination**:
   - **Body**: Plays flinch animation
   - **Vision**: Updates threat assessment
   - **Intention**: Evaluates if retreat is needed
   - **Locomotion**: Prepares retreat movement if needed
4. **Behavior Change**: Intention may switch to RetreatBehavior
5. **Action Execution**: New behavior's actions are executed

This component-based approach provides:
- **Modularity**: Each component handles one aspect of AI
- **Flexibility**: Components can be mixed and matched
- **Maintainability**: Easy to modify individual components
- **Reusability**: Components can be shared between different bot types

This analysis provides the foundation for implementing a robust AI system in Godot that follows the proven patterns from the Nextbot system while adapting to Godot's architecture and capabilities. 