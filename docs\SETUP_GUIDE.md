# NextBot System Setup Guide

## 🚨 CRITICAL: Fix "NextBotManager not declared" Error

If you're getting this error:
```
Parser Error: Identifier "NextBotManager" not declared in the current scope.
```

**This means the autoload is not set up correctly. This is REQUIRED for the system to work.**

## Quick Fix for Parse Errors

The NextBot system **CANNOT WORK** without proper autoload setup. Follow these steps:

### 1. Set Up NextBot Manager as Autoload

**🚨 CRITICAL STEP - SYSTEM WILL NOT WORK WITHOUT THIS**

1. Open Godot and go to **Project → Project Settings**
2. Click on the **Autoload** tab
3. Click the folder icon next to "Path"
4. Navigate to and select `scripts/enemy/nextbot_manager.gd`
5. Set the "Name" field to: `NextBotManager` (exact spelling!)
6. Make sure "Enable" is checked
7. Click "Add"
8. **RESTART GODOT** (close and reopen the project)

**Verify it worked**: You should see NextBotManager in the autoload list with a checkmark.

### 2. Test the System

**If you're still getting "NextBotManager not declared" errors:**
1. **STOP** - The autoload is not set up correctly
2. **Read**: `CRITICAL_SETUP_FIX.md` for detailed troubleshooting
3. **Try**: `scripts/basic_test_no_autoload.gd` to test if scripts load correctly

**Once autoload is working:**

**Option A: Quick Test (Recommended)**
1. Create a new 3D scene in Godot
2. Add a Node3D as the root
3. Attach the script `scripts/simple_nextbot_test.gd` to the root node
4. Run the scene - it will automatically test the NextBot system
5. Check the console output for test results

**Option B: Manual Test**
1. Create a new 3D scene
2. Add a Node3D as root and attach `scripts/test_nextbot_scene.gd`
3. Run the scene to create a test bot programmatically
4. Use the console commands to test functionality

### 3. Create Your Own NextBot

#### Option A: Use the Enhanced NextBot (Recommended)

```gdscript
# Create a new scene with EnhancedNextBot as root
extends EnhancedNextBot

func _ready():
    super._ready()
    
    # Set bot properties
    health = 100.0
    team = 1
    debug_name = "MyBot"
    
    # The bot will automatically start with IdleAction
```

#### Option B: Create a Custom NextBot

```gdscript
# Create a custom bot extending INextBot
extends INextBot

# Implement required interface methods
func get_locomotion_interface():
    return $Components/MyLocomotionComponent

func get_body_interface():
    return $Components/MyBodyComponent

func get_vision_interface():
    return $Components/MyVisionComponent

func get_intention_interface():
    return $Components/MyIntentionComponent
```

### 4. Scene Structure

Your NextBot scene should have this structure:

```
MyNextBot (extends EnhancedNextBot or INextBot)
├── NavigationAgent3D
├── CollisionShape3D
├── MeshInstance3D
├── AnimationPlayer (optional)
├── Components/
│   ├── [Enhanced or regular components]
└── RayCasts/ (optional)
    └── VisionRayCast (RayCast3D)
```

### 5. Navigation Setup

For bots to move properly:

1. Add a **NavigationRegion3D** to your scene
2. Set up a navigation mesh for your level
3. Make sure your bot's **NavigationAgent3D** is configured:
   - Set appropriate radius and height
   - Configure avoidance settings if needed

### 6. Common Issues and Solutions

#### "Parse error" on script files
- **Solution**: Make sure NextBotManager is set up as autoload (step 1)
- **Solution**: Check that all script files are in the correct directories

#### "Cannot load test scene" error
- **Solution**: Use the simple test scripts instead of .tscn files
- **Solution**: Create scenes manually in Godot editor

#### Bot doesn't move
- **Solution**: Check NavigationRegion3D and navigation mesh setup
- **Solution**: Verify NavigationAgent3D configuration

#### Components not working
- **Solution**: Make sure component nodes exist in the scene
- **Solution**: Check that component scripts are assigned correctly

#### Actions not changing
- **Solution**: Verify the intention component is working
- **Solution**: Check debug output for action changes

#### Test scripts show errors
- **Solution**: Run the simple test first: `scripts/simple_nextbot_test.gd`
- **Solution**: Check console output for specific error messages

### 7. Debugging

Enable debug output:

```gdscript
# In your bot's _ready() function
NextBotManager.set_debug_types(NextBotManager.DebugType.ALL)
```

Debug hotkeys (in debug builds):
- **Ctrl+F1**: Toggle behavior debugging
- **Ctrl+F2**: Toggle path debugging
- **Ctrl+F3**: Toggle vision debugging

### 8. Example Usage

```gdscript
# Simple patrol bot
extends EnhancedNextBot

func _ready():
    super._ready()
    
    # Set up patrol points
    var patrol_points = [
        Vector3(0, 0, 0),
        Vector3(10, 0, 0),
        Vector3(10, 0, 10),
        Vector3(0, 0, 10)
    ]
    
    # Start patrolling
    var intention = get_intention_interface()
    if intention:
        var patrol_action = PatrolAction.new()
        patrol_action.patrol_points = patrol_points
        intention.change_action(patrol_action)
```

### 9. Performance Tips

- Use `NextBotManager.bots_per_frame` to control update frequency
- Enable distance culling for large numbers of bots
- Use the throttled update system for better performance

### 10. Next Steps

1. Read the full documentation in `NEXTBOT_SYSTEM_README.md`
2. Explore the example actions in `scripts/enemy/actions/`
3. Create custom actions for your specific game needs
4. Set up proper navigation meshes for your levels

## Need Help?

If you're still having issues:

1. Check the console for error messages
2. Verify all file paths are correct
3. Make sure you're using Godot 4.4 or later
4. Ensure the NextBotManager autoload is properly configured

The system should work out of the box once the autoload is set up correctly!
