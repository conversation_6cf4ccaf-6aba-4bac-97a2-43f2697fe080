# Music System Guide

## Overview

The new music system allows you to create sets of songs that are paired together for different game states (pre-game, normal game, doom, and overwhelm). Each song set contains 3-4 audio files that play during different phases of the game. The system automatically selects random music sets for variety and handles smooth transitions between tracks.

## Components

### 1. MusicSet Resource
A resource that defines a complete set of music tracks for different game states:

```gdscript
class_name MusicSet
extends Resource

@export var set_name: String = "Default Set"
@export var pre_game_track: AudioStream
@export var normal_game_track: AudioStream
@export var doom_track: AudioStream
@export var overwhelm_track: AudioStream  # Optional
@export var description: String = ""
@export var tags: Array[String] = []
```

### 2. MusicManager Component
Manages music transitions and handles random selection of music sets:

```gdscript
class_name MusicManager
extends Node

@export var music_sets: Array[MusicSet] = []
@export var fade_duration: float = 1.0
@export var auto_randomize: bool = true
```

### 3. GameMasterMusicController Component
Integrates the MusicManager with the existing GameMaster system:

```gdscript
class_name GameMasterMusicController
extends Node

@export var music_manager: MusicManager
@export var gamemaster_path: NodePath
```

### 4. Enhanced GameMaster
An enhanced version of the original GameMaster that integrates with the music system while maintaining backward compatibility.

## Setup Instructions

### Step 1: Create Music Sets

1. In the Godot editor, right-click in the FileSystem panel
2. Select "New Resource" → "MusicSet"
3. Configure the music set:
   - **Set Name**: A descriptive name for the set
   - **Pre Game Track**: Audio file for the pre-game state
   - **Normal Game Track**: Audio file for normal gameplay
   - **Doom Track**: Audio file for the doom countdown
   - **Overwhelm Track**: Audio file for when doom expires (optional)
   - **Description**: Optional description
   - **Tags**: Optional tags for organization

### Step 2: Set Up the Scene

1. Add a `MusicManager` node to your scene
2. Add an `AudioStreamPlayer` node (typically under `env/bgm`)
3. Add the `GameMasterMusicController` node
4. Configure the connections:
   - Set the `MusicManager` reference in the controller
   - Set the `GameMaster` path in the controller

### Step 3: Configure Music Sets

1. Select the `MusicManager` node
2. In the inspector, expand the "Music Sets" array
3. Add your created `MusicSet` resources to the array
4. Configure the fade duration and auto-randomize settings

### Step 4: Integrate with GameMaster

Option A: Use the Enhanced GameMaster
- Replace your existing GameMaster with `gamemaster_enhanced.gd`
- Set the `music_manager` reference in the inspector

Option B: Use the Music Controller
- Keep your existing GameMaster
- Add the `GameMasterMusicController` component
- Configure the connections

## Usage Examples

### Creating a Music Set Programmatically

```gdscript
# Create a new music set
var music_set = MusicSet.new()
music_set.set_name = "Epic Battle Set"
music_set.pre_game_track = preload("res://assets/music/epic_pre.ogg")
music_set.normal_game_track = preload("res://assets/music/epic_battle.ogg")
music_set.doom_track = preload("res://assets/music/epic_doom.ogg")
music_set.overwhelm_track = preload("res://assets/music/epic_overwhelm.ogg")

# Add to music manager
music_manager.add_music_set(music_set)
```

### Manual Music Control

```gdscript
# Select a random music set
music_manager.select_random_music_set()

# Select a specific music set
music_manager.select_music_set(0)

# Manually trigger music changes
music_manager.play_state_music(MusicManager.GameState.NORMAL_GAME)
music_manager.play_state_music(MusicManager.GameState.DOOM)
music_manager.play_state_music(MusicManager.GameState.OVERWHELM)
```

### Listening to Music Events

```gdscript
# Connect to music change events
music_manager.music_changed.connect(_on_music_changed)
music_manager.music_set_changed.connect(_on_music_set_changed)

func _on_music_changed(track_name: String, state: MusicManager.GameState):
    print("Music changed to: ", track_name, " (State: ", state, ")")

func _on_music_set_changed(set_name: String):
    print("Music set changed to: ", set_name)
```

## Game States

The system supports four game states:

1. **PRE_GAME**: Before the bell rings
2. **NORMAL_GAME**: During normal gameplay
3. **DOOM**: During the doom countdown
4. **OVERWHELM**: When doom expires and enemies charge

## Automatic Transitions

The system automatically handles music transitions based on game events:

- **Bell rings**: Pre-game → Normal game
- **Doom starts**: Normal game → Doom
- **Doom expires**: Doom → Overwhelm
- **Player dies**: Any state → Pre-game

## Random Selection

The system automatically selects random music sets for variety:

- **On scene load**: If `auto_randomize` is enabled
- **Manual selection**: Call `select_random_music_set()`
- **Programmatic selection**: Call `select_music_set(index)`

## Fade Transitions

All music transitions use smooth fade effects:

- **Fade out**: Current track fades out over half the duration
- **Track change**: New track is loaded
- **Fade in**: New track fades in over half the duration

## Backward Compatibility

The enhanced GameMaster maintains full backward compatibility:

- Legacy BGM system still works if no MusicManager is assigned
- All existing GameMaster functionality is preserved
- Gradual migration is supported

## Best Practices

1. **Organize Music Sets**: Use descriptive names and tags
2. **Consistent Audio Levels**: Ensure all tracks have similar volume levels
3. **Appropriate Fade Duration**: Use 1-2 seconds for smooth transitions
4. **Test Transitions**: Verify all state transitions work correctly
5. **Performance**: Keep audio files optimized for your target platform

## Troubleshooting

### No Music Playing
- Check that the AudioStreamPlayer is in the correct bus
- Verify the music sets have valid audio files
- Ensure the MusicManager is properly connected

### Transitions Not Working
- Check that the GameMaster signals are properly connected
- Verify the MusicManager is assigned in the GameMaster
- Ensure the audio files are valid and loaded

### Random Selection Not Working
- Verify that music sets are added to the MusicManager
- Check that `auto_randomize` is enabled
- Ensure the MusicManager is properly initialized

## Example Scene Structure

```
Scene Root
├── MusicManager
├── GameMaster (enhanced)
├── GameMasterMusicController
├── env
│   └── bgm (AudioStreamPlayer)
├── UI
│   ├── TimerLabel
│   ├── MSLabel
│   ├── KillsLabel
│   ├── DoomLabel
│   ├── OverwhelmMeter
│   └── ReaperOverlay
├── EnemySpawnZones
├── Bell
└── Player
```

This structure provides a complete music system that integrates seamlessly with your existing game architecture while providing the flexibility and variety you requested. 