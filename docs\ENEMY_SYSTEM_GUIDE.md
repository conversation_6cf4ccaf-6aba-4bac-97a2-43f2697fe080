# BaseEnemy System Guide

## Overview
The BaseEnemy system provides a clean, reusable foundation for creating different enemy types. Your original knight code (352 lines) is replaced with focused, extendable scripts.

## System Comparison

### Before (knight_fullv1.gd)
- ✅ Works but is monolithic
- ❌ 352 lines of mixed concerns  
- ❌ Hard to reuse for other enemy types
- ❌ Must copy-paste and modify for variations

### After (BaseEnemy system)
- ✅ BaseEnemy.gd: 313 lines of reusable foundation
- ✅ KnightV2.gd: 117 lines of knight-specific behavior
- ✅ ArcherEnemy.gd: 167 lines of archer-specific behavior
- ✅ Easy to create new enemy types

## Quick Start

### 1. Basic Enemy Scene Setup
Create a new scene with this structure:
```
EnemyRoot (CharacterBody3D) [attach your enemy script]
├── MeshInstance3D (your 3D model)
├── CollisionShape3D (collision shape)
├── NavigationAgent3D (optional - for pathfinding)
└── StateLabel (Label3D, optional - for debugging)
```

### 2. Creating a New Enemy Type
```gdscript
extends BaseEnemy
class_name MyCustomEnemy

# Add your custom properties
@export var special_ability_cooldown: float = 5.0

func setup_enemy():
    # Custom initialization
    print("Custom enemy ready!")

# Override specific behaviors you want to change
func do_chase(delta: float):
    # Custom chase behavior
    super.do_chase(delta)  # Call parent behavior first
    # Add your modifications here

func perform_attack():
    # Custom attack behavior
    # Your custom attack code here
```

## Available States
- **IDLE**: Standing around, looking for player
- **PATROL**: Moving along a patrol route (override for custom patrol)
- **CHASE**: Pursuing the player
- **ATTACK**: Performing attack behavior
- **STUNNED**: Briefly disabled (when taking damage)
- **DEAD**: Enemy is defeated

## Editor Properties

### Movement Group
- `movement_speed`: How fast the enemy moves when chasing
- `patrol_speed`: How fast the enemy moves when patrolling  
- `turn_speed`: How quickly the enemy rotates to face targets

### Combat Group
- `attack_range`: Distance at which enemy can attack
- `attack_damage`: Damage dealt per attack
- `attack_cooldown`: Time between attacks

### Detection Group
- `sight_range`: How far the enemy can see the player
- `hearing_range`: How far the enemy can hear (for future use)
- `lose_target_time`: How long before giving up chase

### Health Group
- `max_health`: Enemy's starting health

### Debug Group
- `debug_enabled`: Print debug messages to console
- `show_state_label`: Show floating state label above enemy

## Example Enemy Types

### Knight (Melee Fighter)
```gdscript
# KnightV2 - charges at player, deals knockback damage
extends BaseEnemy

# Key features:
# - Charges faster when close to player
# - Launches player backward when attacking
# - Gets angry when taking damage
```

### Archer (Ranged Fighter)  
```gdscript
# ArcherEnemy - keeps distance, shoots projectiles
extends BaseEnemy

# Key features:
# - Maintains preferred distance from player
# - Retreats if player gets too close  
# - Aims before shooting
# - Requires projectile_scene to be assigned
```

## Making New Enemy Types

### Step 1: Create the Script
```gdscript
extends BaseEnemy
class_name YourEnemyName

func setup_enemy():
    # Your initialization here
    pass

# Override the methods you want to customize
func do_chase(delta: float):
    # Your custom chase behavior
    pass

func perform_attack():
    # Your custom attack behavior  
    pass
```

### Step 2: Create the Scene
1. New scene → CharacterBody3D
2. Add MeshInstance3D with your model
3. Add CollisionShape3D  
4. Add NavigationAgent3D (optional)
5. Add Label3D named "StateLabel" (optional debug)
6. Attach your script to root node
7. Configure properties in inspector

### Step 3: Configure in Editor
- Set movement speeds, attack values, etc.
- Assign projectile scene (for ranged enemies)
- Enable debug options if needed
- Test in game!

## Advanced Customization

### Override State Enter/Exit
```gdscript
func enter_chase():
    super.enter_chase()  # Call parent
    # Your custom enter logic

func exit_attack():
    super.exit_attack()  # Call parent
    # Your custom exit logic
```

### Add Custom States
You can extend the state enum and add new states:
```gdscript
enum CustomState { SPECIAL_ABILITY = 100 }

func _execute_state(delta: float):
    if current_state == CustomState.SPECIAL_ABILITY:
        do_special_ability(delta)
    else:
        super._execute_state(delta)
```

## Debugging

### Visual State Display
Enable `show_state_label` to see enemy state above their head:
- White: IDLE
- Blue: PATROL  
- Yellow: CHASE
- Red: ATTACK
- Orange: STUNNED
- Gray: DEAD

### Console Debug
Enable `debug_enabled` for detailed console output:
```
[KnightV2] State: IDLE -> CHASE
[KnightV2] Charging attack!
[KnightV2] Sword slash! Damage: 25
```

## Performance Tips
- Use NavigationAgent3D for complex level geometry
- Disable NavigationAgent3D for simple, direct movement  
- Adjust sight_range based on level size
- Use debug sparingly in production builds

## Extending the System
The BaseEnemy class is designed to be extended. Common additions:
- Patrol waypoint system
- Group coordination (pack behavior)
- Special abilities with cooldowns
- Animation integration
- Sound effect triggers
- Damage type resistance

Your original knight still works perfectly - this system gives you options for rapid enemy prototyping!