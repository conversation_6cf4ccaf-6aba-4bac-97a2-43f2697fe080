# Archer Enemy Projectile Fixes

## Issues Identified and Fixed

### 1. Attack Range Problem
**Issue**: <PERSON> was inheriting melee attack range (2.0 units) from BaseEnemy, causing it to try to get too close before attacking.

**Fix**: 
- Set `attack_range = preferred_distance + 2.0` in `setup_enemy()` 
- This gives archer proper ranged attack range (~10 units)

### 2. Projectile Collision Detection
**Issue**: Projectile RigidBody3D wasn't properly detecting collisions with player.

**Fixes**:
- Added `contact_monitor = true` and `max_contacts_reported = 10` to projectile scene
- Added `collision_mask = 3` to ensure projectiles interact with player collision layer
- Enhanced collision handling in projectile script

### 3. Projectile Setup and Targeting
**Issue**: Inconsistent projectile velocity setting and poor targeting.

**Fixes**:
- Enhanced `shoot_projectile()` method with multiple fallback velocity setting methods
- Improved targeting to aim at player's center (`+ Vector3(0, 1.0, 0)`)
- Added projectile orientation using `look_at()` for proper arrow direction
- Better error handling and debug output

### 4. Debug and Testing
**Issue**: Hard to diagnose what was going wrong.

**Fixes**:
- Added comprehensive debug output to archer chase state
- Enhanced projectile script with collision debugging
- Created test scene (`scenes/archer_test.tscn`) with test controller
- Added manual testing functions (press 1-5 keys for different tests)

## Files Modified

1. `enemies/projectile_3d.gd` - Enhanced collision detection and debugging
2. `enemies/projectile_3d.tscn` - Fixed collision settings
3. `scripts/enemy/ArcherEnemy.gd` - Fixed attack range and improved logic
4. `scenes/archer_test.tscn` - New test scene
5. `scripts/test_player.gd` - Test player with damage handling
6. `scripts/archer_test_controller.gd` - Debug controller for testing

## Testing

To test the fixes:
1. Open `scenes/archer_test.tscn`
2. Run the scene
3. Use WASD to move the test player
4. Press 1-5 for various debug tests:
   - 1: Print archer status
   - 2: Force archer to attack
   - 3: Check distances
   - 4: Check line of sight
   - 5: Spawn test projectile

The archer should now:
- Detect the player when in sight range
- Move to preferred distance (8 units)
- Attack with projectiles when in range (4-10 units)
- Retreat if player gets too close (< 4 units)
- Projectiles should hit and damage the player

## Key Settings

- **Attack Range**: 10.0 (preferred_distance + 2.0)
- **Preferred Distance**: 8.0
- **Min Distance**: 4.0 (retreat threshold)
- **Projectile Speed**: 20.0
- **Sight Range**: 10.0 (inherited from BaseEnemy)
