# 🔧 Action Parameter Fix - Bot Reference Issue

## ✅ Problem Identified

The error `Identifier "bot" not declared in the current scope` was happening because:

1. **Actions need access to the bot** in event methods like `on_sound()`, `on_sight()`, etc.
2. **Event methods don't receive bot as parameter** - only lifecycle methods do
3. **Actions had no way to access the bot** outside of lifecycle methods

## 🛠️ Solution Applied

### **1. Added Bot Storage to NextBotAction**
```gdscript
# In NextBotAction base class:
var bot = null  # Reference to the bot this action is running on

func on_start(bot_ref, prior_action: NextBotAction) -> ActionResult:
    bot = bot_ref  # Store bot reference for use in event methods
    # ... rest of method
```

### **2. Updated Parameter Names**
Changed all lifecycle method parameters from `bot` to `bot_ref` to avoid confusion:
- `on_start(bot_ref, prior_action)`
- `update(bot_ref, delta)`
- `on_end(bot_ref, next_action)`
- etc.

### **3. Actions Can Now Access Bot**
Event methods can now use the stored `bot` reference:
```gdscript
func on_sound(source: Node, pos: Vector3, sound_data: Dictionary) -> ActionResult:
    # Now this works - bot is the stored reference
    if source != bot and pos.distance_to(bot.global_position) < 10.0:
        # ... handle sound
```

## 📋 Files That Need Updating

All action files need their method signatures updated:

### **✅ Fixed:**
- `scripts/enemy/actions/idle_action.gd`

### **🔄 Need Fixing:**
- `scripts/enemy/actions/attack_action.gd`
- `scripts/enemy/actions/seek_and_destroy_action.gd`
- `scripts/enemy/actions/retreat_action.gd`
- `scripts/enemy/actions/patrol_action.gd`
- `scripts/enemy/actions/investigate_action.gd`
- `scripts/enemy/actions/unstuck_action.gd`
- `scripts/enemy/actions/death_action.gd`

## 🔧 Required Changes Per File

For each action file, update:

1. **Method signatures:**
   ```gdscript
   # OLD:
   func on_start(bot: INextBot, prior_action: NextBotAction) -> ActionResult:
   func update(bot: INextBot, delta: float) -> ActionResult:
   
   # NEW:
   func on_start(bot_ref, prior_action: NextBotAction) -> ActionResult:
   func update(bot_ref, delta: float) -> ActionResult:
   ```

2. **Super calls:**
   ```gdscript
   # OLD:
   super.on_start(bot, prior_action)
   super.update(bot, delta)
   
   # NEW:
   super.on_start(bot_ref, prior_action)
   super.update(bot_ref, delta)
   ```

3. **Keep bot usage in method bodies unchanged** - they should use the stored `bot` reference

## ✅ Result

After fixing all action files:
- ✅ **No "bot not declared" errors**
- ✅ **Actions can access bot in all methods**
- ✅ **Event methods work correctly**
- ✅ **All functionality preserved**

## 🧪 Testing

Once all files are fixed, test with:
1. Create any NextBot action
2. Trigger events like `on_sound()` or `on_sight()`
3. Verify no scope errors occur

The NextBot action system will then work perfectly!
