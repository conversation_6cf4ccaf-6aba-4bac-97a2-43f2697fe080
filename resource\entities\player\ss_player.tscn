[gd_scene load_steps=63 format=4 uid="uid://ceh84ae048v4y"]

[ext_resource type="Script" uid="uid://b8548jsvjq2xi" path="res://addons/GoldGdt/src/GoldGdt_Pawn.gd" id="1_jcds0"]
[ext_resource type="Script" uid="uid://cnjcvbcitkf4q" path="res://resource/entities/player/speedometer.gd" id="2_j78vv"]
[ext_resource type="FontFile" uid="uid://cuxa5noqja2ow" path="res://assets/ui/IosevkaStiletto-ExtendedLight.ttf" id="2_oamj2"]
[ext_resource type="Script" uid="uid://deqk52egnd0bu" path="res://addons/GoldGdt/src/GoldGdt_DebugUI.gd" id="2_rorqd"]
[ext_resource type="PackedScene" uid="uid://7c8n402dhju" path="res://addons/grappling_hook_3d/src/charge_indicatorv2.tscn" id="2_tknep"]
[ext_resource type="Texture2D" uid="uid://cskw882fffi36" path="res://addons/grappling_hook_3d/example/hook_availible.png" id="3_7e61h"]
[ext_resource type="Script" uid="uid://b0jvghkrvkrm" path="res://addons/GoldGdt/src/GoldGdt_Controls.gd" id="3_mnwgk"]
[ext_resource type="Resource" uid="uid://c770d2068in3p" path="res://addons/GoldGdt/Default.tres" id="4_gs0bd"]
[ext_resource type="Script" uid="uid://csey84npedtrn" path="res://addons/GoldGdt/src/GoldGdt_View.gd" id="5_nq1ha"]
[ext_resource type="PackedScene" uid="uid://dht30mn7psvj1" path="res://scenes/ui/ammoindicator.tscn" id="6_6icka"]
[ext_resource type="Script" uid="uid://btwg0qf5dwemu" path="res://addons/GoldGdt/src/GoldGdt_Move.gd" id="6_gbin3"]
[ext_resource type="PackedScene" uid="uid://upoffslexyvo" path="res://scenes/ui/hp.tscn" id="7_8kt16"]
[ext_resource type="Script" uid="uid://dxe4j77d60noj" path="res://addons/controller_visualizer/controller_visualizer.gd" id="7_gsdvn"]
[ext_resource type="Script" uid="uid://dcb5tvbgw87hn" path="res://addons/GoldGdt/src/GoldGdt_Body.gd" id="7_mapp2"]
[ext_resource type="Script" uid="uid://drur6ejin23t2" path="res://addons/GoldGdt/src/GoldGdt_Camera.gd" id="8_3kmgh"]
[ext_resource type="FontFile" uid="uid://7v1pb3ogj7y7" path="res://assets/ui/04B_09__.TTF" id="8_iwcrl"]
[ext_resource type="AudioStream" uid="uid://c5m5rhtnv21ml" path="res://assets/player/sound/footsteps/step.wav" id="9_j78vv"]
[ext_resource type="PackedScene" uid="uid://w4c063ol3oeq" path="res://addons/GoldGdt/FootstepManager.tscn" id="10_4lxdi"]
[ext_resource type="PackedScene" uid="uid://c8f842bvgx67d" path="res://scenes/v_revolver3.tscn" id="10_vqa1p"]
[ext_resource type="AudioStream" uid="uid://bsggcctk3rvhk" path="res://assets/player/sound/footsteps/step3.wav" id="11_7e61h"]
[ext_resource type="AudioStream" uid="uid://cfhn162snc45k" path="res://assets/player/sound/footsteps/step4.wav" id="11_j78vv"]
[ext_resource type="Script" uid="uid://cv828fvg40vnc" path="res://resource/scripts/wall_movement.gd" id="15_7omu7"]
[ext_resource type="Script" uid="uid://dmy2w5b50d86e" path="res://resource/entities/player/viewmodel_lag.gd" id="16_03rbn"]
[ext_resource type="AudioStream" uid="uid://cyuxpbdv2dj8u" path="res://assets/player/sound/hardlanding.ogg" id="17_ootft"]
[ext_resource type="Material" uid="uid://bb43ngplsfis2" path="res://addons/flexible_toon_shader/FlexibleToonMaterial.tres" id="21_6icka"]
[ext_resource type="Script" uid="uid://7s50gitcs8s0" path="res://assets/player/player/player_model_compass.gd" id="21_fglin"]
[ext_resource type="AudioStream" uid="uid://e83b30q24do8" path="res://assets/player/sound/playerdeath.ogg" id="21_icefa"]
[ext_resource type="AudioStream" uid="uid://dg1hppprd7ocq" path="res://assets/player/sound/playerpain00.ogg" id="22_ja8ns"]
[ext_resource type="AudioStream" uid="uid://bu2krcgjnfmba" path="res://assets/player/sound/playerpain01.ogg" id="23_omgdn"]
[ext_resource type="Script" uid="uid://c68yqrkkxup8g" path="res://resource/scripts/PlayerCharacter/knockback_module.gd" id="23_yi2sc"]
[ext_resource type="AudioStream" uid="uid://leehc33or3xb" path="res://assets/player/sound/playerpain02.ogg" id="24_8kt16"]
[ext_resource type="Script" uid="uid://buho48oa1ii1x" path="res://resource/scripts/PlayerCharacter/kickback_module.gd" id="24_v3lrb"]
[ext_resource type="Shader" uid="uid://d2fgnj7k78og" path="res://assets/player/player/textures/viewmodel.gdshader" id="25_5hvdj"]
[ext_resource type="Material" uid="uid://dpsb8b4ts8xg5" path="res://assets/player/ss_player_viewmodel.tres" id="25_6icka"]
[ext_resource type="AudioStream" uid="uid://vi528wyc5ebr" path="res://assets/player/sound/clangv2.wav" id="25_ejsji"]
[ext_resource type="AudioStream" uid="uid://dn7yuv0pgv5hs" path="res://assets/player/sound/playerpain03.ogg" id="25_ggent"]
[ext_resource type="Script" uid="uid://d1jfc1437h3db" path="res://resource/entities/player/player_health.gd" id="27_ggent"]
[ext_resource type="PackedScene" uid="uid://fhq4dg77kclt" path="res://assets/player/player/compass.tscn" id="27_ouq8c"]
[ext_resource type="AudioStream" uid="uid://lkt1vkbk8ry8" path="res://assets/player/sound/airdodge.ogg" id="35_5hvdj"]
[ext_resource type="Script" uid="uid://c77sg1frmiydi" path="res://resource/entities/player/player_state.gd" id="35_eqdhr"]
[ext_resource type="PackedScene" uid="uid://btwrtq7s18lje" path="res://resource/entities/player/weapon_system.tscn" id="36_7tjvu"]
[ext_resource type="AudioStream" uid="uid://b8buayac52x1c" path="res://assets/player/sound/kickbackv3.ogg" id="45_ja8ns"]
[ext_resource type="PackedScene" uid="uid://dt42fklyynvbr" path="res://resource/entities/player/bigshot_proj.tscn" id="48_8kt16"]
[ext_resource type="PackedScene" uid="uid://cvnedpp1pj7o6" path="res://HookV2.tscn" id="48_icefa"]
[ext_resource type="AudioStream" uid="uid://3jjbfxm6iwvg" path="res://assets/player/sound/gunshotv4.ogg" id="49_8kt16"]
[ext_resource type="AudioStream" uid="uid://p81tavbhjp3d" path="res://assets/player/sound/bigshotv2.ogg" id="50_8kt16"]
[ext_resource type="AudioStream" uid="uid://wjcqnvs6go5h" path="res://assets/player/sound/hook_retractv1.wav" id="50_321qo"]
[ext_resource type="PackedScene" uid="uid://p262qaitjp3" path="res://scenes/effects/bullet_impact.tscn" id="50_fa17f"]
[ext_resource type="PackedScene" uid="uid://bq8x7y21aw3m4" path="res://scenes/effects/bullet_trail.tscn" id="51_4y3xk"]
[ext_resource type="PackedScene" uid="uid://bb1ki6ldu1bfx" path="res://scenes/effects/muzzle_flash.tscn" id="52_5mmtj"]
[ext_resource type="PackedScene" uid="uid://cn8nx1ktyrp0e" path="res://assets/effect/bullethole2.tscn" id="53_vijo2"]

[sub_resource type="LabelSettings" id="LabelSettings_bskcs"]
font = ExtResource("2_oamj2")

[sub_resource type="LabelSettings" id="LabelSettings_ja8ns"]
font = ExtResource("8_iwcrl")
font_size = 8
font_color = Color(1, 1, 1, 0.47451)

[sub_resource type="BoxShape3D" id="BoxShape3D_j7pn1"]
size = Vector3(0.813, 1.829, 0.813)

[sub_resource type="StandardMaterial3D" id="StandardMaterial3D_8maty"]
resource_name = "SokkyComm_CellShade_MAT"
transparency = 4
cull_mode = 2
metallic = 1.0

[sub_resource type="ArrayMesh" id="ArrayMesh_hwnqk"]
resource_name = "SokkyComm2_SokkyComm"
_surfaces = [{
"aabb": AABB(-1.96087, -0.0164901, -0.530815, 3.92175, 6.19896, 1.17751),
"attribute_data": PackedByteArray("dH0QP9uTej/oMP07P3NfP76wLz+HQVY/6DD9Oz9zXz/gvzE7rfA8P76wLz+HQVY/h68rP4wWdD+o9x8/wTU1P6j3Hz/BNTU/4L8xO63wPD8NAS8/0z9JP/NnNT/iZGY/z+k4P9fBbj9dDVA/MaxSP/NnNT/iZGY/DQEvP9M/ST9dDVA/MaxSP7YTcj+V80M/hqhzPyGxUD+2E3I/lfNDPwnJej8wZlE/RfhyP1teQz861X0/grk/P/crdz9/4T0/Iad0P+aAUT/3K3c/f+E9PzrVfT+CuT8/RfhyP1teQz/YLYc+zIc5PkTXnT4M1uc+aPpfPbz3OD6GLpg7XK/nPpU6xz6Abn49177VPmg/hz0dJPI+wNZePiVZ6T5YqtY9gSf1Psgh9z3ZP/A+oH/lPcL1+j6weec9kIimPgBEiDwvn70+oEGlPOmPmT4geoI8kIimPgBEiDyVOsc+gG5+PS+fvT6gQaU8177VPmg/hz0dJPI+wNZePiVZ6T5YqtY9wvX6PrB55z2BJ/U+yCH3Pdk/8D6gf+U9HRkEP2psjz7s4Oo+wkutPuzg6j7CS60+KGZhPbZ8Az8Yjiw90FQTP+C0izw8NgY/KGZhPbZ8Az/kdZA9plQSP+R1kD2mVBI/YIIDPjrfAz8gW/M9XAsRPyBb8z1cCxE/YIIDPjrfAz98VA8+0FQTP9QUJD42XAc/R5ypPm7iFj8euoo+nkYAPzQilT4i9wU/6LOuPshhBj9R97o+hq8EP+VArD7CZPk+Ufe6PoavBD/rs64+yGEGPzQilT4i9wU/KydFPixuFj+qx08/EBwPP+h8Oz+zfxU/ZLNCPwaW/T46yU0/dLL3PjU8Nz+QeQo/BnBHPx7Z7j404yo/6eYLP2SzQj8Glv0+yl4nP9mmIT/+aRQ/SOIRPzmpDD/ydSM/qsdPPxAcDz/ofDs/s38VPzU8Nz+QeQo/NOMqP+nmCz/KXic/2aYhP/5pFD9I4hE/OakMP/J1Iz9IEjg/gP5UPoT/Lj9Q9kw+SBI4P4D+VD5Up0U/8Fo5PuArQz9wflw+VKdFP/BaOT7gK0M/cH5cPlKbTz/o3RA+UptPP+jdED64EUI/AIKkPf0cVz8YL08+lk5UPzB/Kj4U9lw/gAJAPsl5SD/4emI+EHRKP7AOiT4WYE4/yNygPu61Rz8MaqI+/tZYP6x3gj52JlM/bAWjPv0cVz8YL08+lk5UPzB/Kj4U9lw/gAJAPsl5SD/4emI+FmBOP8jcoD4QdEo/sA6JPu61Rz8MaqI+/tZYP6x3gj52JlM/bAWjPqr/PT9kfo0+A4BBPyiAnD4YJzw/CDucPqo9Qz8gfpE+uttHP2DUdD7ROz0/vDuJPqrAQz8IVXU+JZFBP/gFiz62rlc/AIXuPEJ5Zz8AVZk9XPtRPzCdpj0Fzmg/gOLDPP2jTz8grQk9jSN+PyBTiz30qD8/QFcQPShweD9A9Zw8NY4+P2DlWj0v3zE/AH1dPB3wLj8AsL88Bc5oP4DiwzxCeWc/AFWZPbauVz8Ahe48XPtRPzCdpj39o08/IK0JPfSoPz9AVxA9NY4+P2DlWj0v3zE/AH1dPB3wLj8AsL88uBJsPyCv8j0uHnk/kK/APQvXbj8QJLM9syV0P/CM+T2ZkXM/8pOUPlKlXD9Q3J8+JmhZP2RKij5gI3M/9q2xPlKlXD9Q3J8+JmhZP2RKij5Bsls/WBRNPnibej+s/ZA+/ilbP+gkgD5CoXo/oJFuPkGyWz9YFE0+/ilbP+gkgD4cEV0/MDUPPqh8fD/wImE+Eh9fPzBkQD6XRX0/wM0kPhwRXT8wNQ8+Eh9fPzBkQD60/GY/gK61PcvpUz9AFgs+J+VGP6BqtD0uW2o/yCILPsvpUz9AFgs+J+VGP6BqtD1fEi8/kokEP2oOJz+8v+8+dsE7P3Tj4T499Rw/MKLWPmoOJz+8v+8+PfUcPzCi1j5fEi8/kokEP99+Hj/4i8Y+dsE7P3Tj4T5scBY/sETBPrbdIj9QyKI+U8wvPy6wxz6tTAY/HCW5Pi1uED8wMKQ++ZvzPvjfpj69Txg/4PKaPj32ET8EKJ0+/jUYP5wzjj6nIx8/MPSSPnmiGD+w5IM+gO4fP9DEeT54Nx8/pMKRPrEuJz+4G5U+uKczP5peoj64pzM/ml6iPlPMLz8usMc+l5g/P/y6yD7ffh4/+IvGPlPMLz8usMc+zIo/P1T20T5TzC8/LrDHPt9+Hj/4i8Y+334eP/iLxj52wTs/dOPhPsyKPz9U9tE+PfUcPzCi1j6Uujc/nMuMPrmcLj+QlmY+sS4nP7gblT54Nx8/pMKRPrmcLj+QlmY+tt0iP1DIoj69Txg/4PKaPmxwFj+wRME+LW4QPzAwpD6tTAY/HCW5Pvmb8z7436Y+PfYRPwQonT7+NRg/nDOOPqcjHz8w9JI+eaIYP7Dkgz6A7h8/0MR5PrbdIj9QyKI+uKczP5peoj623SI/UMiiPrinMz+aXqI+tt0iP1DIoj54Nx8/pMKRPrbdIj9QyKI+eDcfP6TCkT54Nx8/pMKRPrbdIj9QyKI+tt0iP1DIoj54Nx8/pMKRPrbdIj9QyKI+eDcfP6TCkT623SI/UMiiPng3Hz+kwpE+YQktPxgUUT7uCSw/jK1vPqEPKj+4HTs+CBIvP/AXWD5ExyU/yNRAPjEyJj8kjFs+9Nk4PzCXJj6E/y4/UPZMPvTZOD8wlyY+SBI4P4D+VD5IEjg/gP5UPuArQz9wflw+zcQxPyD3FT7drj8/IFUwPlSnRT/wWjk+4CtDP3B+XD7drj8/IFUwPlSnRT/wWjk+RAccP1CICD1E+x8/oBAXPdN3Hz8AHeo8Hq0cPyBzKj1vZCI/gBLWPBs4JT/gjMA8r3UrP5D25j2Kqi4/ALDUPcuGJz/IQO89ZYQjP1AH9D0dRR4/CHXMPQx6Hz8gA+09D1oRP4B9NTzDMA4/QHapPHUoDj/AUEo8sc4SP3i2vj0DbxM/AF8DPDGODj9AdsA9QcsFP/CGwT0HPgQ/KMChPQbuFj+QOLg90vgfP5CYqz1jBRc/ABifO8bVDj9w2so9cfAFP0Ch7z36Fgo/YOTLPX6UCz/IjEQ+w8ABP4Db9D22GhM/0KDtPX6UCz/IjEQ+w8ABP4Db9D1x8AU/QKHvPcXVDj9w2so9+hYKP2Dkyz22GhM/0KDtPWElFD9Ii8o9T6gXP3g7wz09aB0/bLorPntsGj+AoVw+JuwqP6hT9z1+lAs/yIxEPsPAAT+A2/Q9cfAFP0Ch7z3G1Q4/cNrKPbYaEz/QoO09cfAFP0Ch7z1+lAs/yIxEPsPAAT+A2/Q9+hYKP2Dkyz22GhM/0KDtPbbdIj9QyKI+eDcfP6TCkT623SI/UMiiPng3Hz+kwpE+tt0iP1DIoj54Nx8/pMKRPng3Hz+kwpE+tt0iP1DIoj54Nx8/pMKRPrbdIj9QyKI+uKczP5peoj64pzM/ml6iPrbdIj9QyKI+uKczP5peoj623SI/UMiiPmj6Xz289zg+hi6YO1yv5z5E150+DNbnPtgthz7Mhzk+KGZhPbZ8Az8Yjiw90FQTP+R1kD2mVBI/4LSLPDw2Bj9gggM+Ot8DPyhmYT22fAM/IFvzPVwLET/kdZA9plQSP3xUDz7QVBM/IFvzPVwLET/UFCQ+NlwHP2CCAz463wM/qFx3PnDiFj80IpU+IvcFPx66ij6eRgA/5UCsPsJk+T7os64+yGEGP1H3uj6GrwQ/Ufe6PoavBD/rs64+yGEGPzQilT4i9wU/qv89P2R+jT4YJzw/CDucPgOAQT8ogJw+qj1DPyB+kT6620c/YNR0PqrAQz8IVXU+0Ts9P7w7iT4lkUE/+AWLPrgSbD8gr/I9Lh55P5CvwD2zJXQ/8Iz5PQvXbj8QJLM9YQktPxgUUT6hDyo/uB07Pu4JLD+MrW8+CBIvP/AXWD5ExyU/yNRAPjEyJj8kjFs+RAccP1CICD1E+x8/oBAXPR6tHD8gcyo903cfPwAd6jxvZCI/gBLWPBs4JT/gjMA8iqouPwCw1D3Lhic/yEDvPa91Kz+Q9uY9HUUePwh1zD1lhCM/UAf0PQx6Hz8gA+09D1oRP4B9NTx1KA4/wFBKPMMwDj9Adqk8sc4SP3i2vj0HPgQ/KMChPQNvEz8AXwM8MY4OP0B2wD1BywU/8IbBPQbuFj+QOLg90vgfP5CYqz1jBRc/ABifO2ElFD9Ii8o9PWgdP2y6Kz5PqBc/eDvDPXtsGj+AoVw+JuwqP6hT9z3G1Q4/cNrKPfoWCj9g5Ms9cfAFP0Ch7z1+lAs/yIxEPsPAAT+A2/Q9thoTP9Cg7T1+lAs/yIxEPnHwBT9Aoe89w8ABP4Db9D3F1Q4/cNrKPfoWCj9g5Ms9thoTP9Cg7T36Fgo/YOTLPXHwBT9Aoe89fpQLP8iMRD7DwAE/gNv0PbYaEz/QoO09thoTP9Cg7T3G1Q4/cNrKPX6UCz/IjEQ+cfAFP0Ch7z3DwAE/gNv0Pa3ZBzxkG0M/9CIfPlJjMD8FTvo9dUs8P4SmjD5cdjQ/fSyIPsrLLD/oAAM+2XdFP1jxBD6C00w/bGJePEb7TT9iGzI+jcdVP9I4UTwyqFk/VNByPqrXTT8yfIY+rjtCPwb0Jj6XP2s/SHItPhAOYD+/9Y4+evVqP7OUjj56uVY/0RWPPmUqYD8MOLk+s4NiP/yF5T61snc/jTygPtdBTD/Fqrw+kvtWP2pJ5z7b5mY/DYkKPwgefD9CuRM/fMNqPzOZvT76ED4/+9vuPvgDST89xhA/RvtXP2/OCz8jwzY/Ble7Pu+HLz8G9uc+bTcqP4E62T4NJRw/aIUKP759JT/lEwM/I/sTP4E62T4NJRw/U6YKP944Rz9LTyU/4NhYP4m8OD9epi4/BvbnPm03Kj8GV7s+74cvPxerlj6ToSw/b84LPyPDNj+Epow+XHY0P9SIWD4EECs/M5m9PvoQPj8FTvo9dUs8P0yPODzHy0g/6AADPtl3RT8yfIY+rjtCP1jxBD6C00w/YhsyPo3HVT+yUGo8XkRUP1TQcj6q100/jTygPtdBTD+zlI4+erlWP0hyLT4QDmA/RIBlPu0BbT/RFY8+ZSpgP/PUnT5nf2o//IXlPrWydz8MOLk+s4NiP8WqvD6S+1Y/aknnPtvmZj9CuRM/fMNqP/vb7j74A0k/PcYQP0b7Vz9Tpgo/3jhHP0tPJT/g2Fg/Qv09P/v8Uz9C/T0/+/xTPxF2Tj+qHWk/gFQ9PwwMRT+AVD0/DAxFP3ztWT/Mqmg/LZxPPwaDRT/1GFw/ipNVPy2cTz8Gg0U/9RhcP4qTVT85IG0//wBkP+UEZT9T4FA/hqhzPyGxUD/lBGU/U+BQP5vDZz8dSUw/beBYP9I6RD+xe1o/pWxJP2VKYz9Xii4/TNhNPyL8Mj9M2E0/IvwyP23gWD/SOkQ/sXtaP6VsST8hp3Q/5oBRPyTLfT+AdUc/JMt9P4B1Rz8UgyU+EIejPeRbZz6o0hc+ltMpPrDyBz4Uj9E94L4YPvi7gT6adJI+oOtRPljHlD7YLyk+/GeSPsT5pj3MvO4+2T3kPdzIgz5qUIU9RhCSPuyg4T3mfJM+4fiiPuQrND7RB7g+UBQIPseUmT44RlQ+h460PswaYT4hrag+DJ1xPsHTpD7A1tg9wdOkPsDW2D3RB7g+UBQIPsd11D5w0To+MgGsPowJfD4m5b8+MEKKPiHA4z7gJEI+BPrvPhheKj6dKQA/xMcLPqXE4j7MLJM++8X/PtyyTD6lxOI+zCyTPsJlzj4gpKE+BPrvPhheKj6dKQA/xMcLPiZEdz5wZA4/rJGgPi5B+T6skaA+LkH5PonRtT7+ffY+tByZPtJ5Dj9Pb0g+NFURP0ecqT5u4hY/lPhuPkIRBT+zHJk+0nkOPysnRT4sbhY/gQIrPmoTCj9NXC89CpgYP8BWNj6cSik/TVwvPQbAKT/AVjY+mNsYP83EMT8g9xU+IW5GP6BKGz5gNTw/4Ly/PSFuRj+gShs+7aY3P9grPD6dxzQ/lvQDP3heMD8Up8A+dKc0Pw5Xjj50pzQ/DleOPglCDj9Qlaw+CUIOP1CVrD7ilf4+ZLqbPu2mNz/YKzw+jAIHP9hpnj28ZhY/oHwXPcQpHD/oaiE+UN8YP5RyHD79GBQ/LOYaPg2oGT8kQFU+85AbP/wUTz65ACA/GEJGPpofJj98jis+MIQOP7gy+j3htQo/eAz7PaUnDj9IOSw+WoUJP2ACOz7htQo/eAz7PTCEDj+4Mvo9cEALPzyXKz6lJw4/SDksPno8CD8McC4+4bUKP3gM+z0whA4/uDL6PeG1Cj94DPs9pScOP0g5LD4UgyU+EIejPaFU8j1A48M9ltMpPrDyBz4Uj9E94L4YPuRbZz6o0hc+2T3kPdzIgz5qUIU9RhCSPuyg4T3mfJM+2C8pPvxnkj5KhG4+fA/tPqDrUT5Yx5Q++LuBPpp0kj4mRHc+cGQOP5T4bj5CEQU/gQIrPmoTCj9Pb0g+NFURPysnRT4sbhY/KydFPixuFj+zHJk+0nkOP7QcmT7SeQ4/R5ypPm7iFj88sJM+/n32PuVArD7CZPk+TVwvPQqYGD9NXC89BsApP9FdvT1OXik/wFY2PpjbGD8XwyQ/cDxVPYwCBz/YaZ49vGYWP6B8Fz3EKRw/6GohPlDfGD+Uchw++mAVPxxKQT4NqBk/JEBVPvOQGz/8FE8+uQAgPxhCRj7pzCQ/wNT4PfC4IT8olk0+MIQOP7gy+j3htQo/eAz7PaUnDj9IOSw+WoUJP2ACOz7htQo/eAz7Pd90Aj9gwAc+ejwIPwxwLj5wQAs/PJcrPjCEDj+4Mvo9pScOP0g5LD7Q7RI/fFcEPuG1Cj94DPs9rlgGPxQ1BT6FAgQ/KKElPjCEDj+4Mvo9MSwSP9ghIj7htQo/eAz7PV5eDj/sDQ0+VVPPPYjVNT9Mjzg8x8tIP9TnbT7D/jU/1IhYPgQQKz+yTvA9s7lUP7JQajxeRFQ//z+bPgjjND8Xq5Y+k6EsP0OVjj6XokE/79WQPmCzTT+tXqs+a5I/P8PDrz6slyU/fHjuPoGeOT+hnr8+FMZKP9Ex6z74Rlc/KKQKPw2LVj8/EAw/hRBqP2deGj+h2Ug/vM+4PmcEcT/fWqE+QxZhP/PUnT5nf2o/9lOgPtCwVT/242Q+I01XP2SjYj7bIWE/RIBlPu0BbT9Uldk99OdmP++m3D1UT10/DYkKPwgefD/8wR4/+/l3P/zBHj/7+Xc/liQcP72jaj/2Jxw/pMFZP6VCKD/J0Gg/DQEvP9M/ST++sC8/h0FWP/NnNT/iZGY/z+k4P9fBbj/zZzU/4mRmP6VCKD/J0Gg/liQcP72jaj++sC8/h0FWPw0BLz/TP0k/9iccP6TBWT9nXho/odlIPz8QDD+FEGo/KKQKPw2LVj/RMes++EZXP7zPuD5nBHE/oZ6/PhTGSj/fWqE+QxZhP/ZToD7QsFU/v/WOPnr1aj9ko2I+2yFhP/bjZD4jTVc/BvQmPpc/az9Uldk99OdmP++m3D1UT10/0jhRPDKoWT+yTvA9s7lUP2xiXjxG+00/rdkHPGQbQz9VU889iNU1P/QiHz5SYzA/1OdtPsP+NT99LIg+ysssP+/VkD5gs00/Q5WOPpeiQT//P5s+COM0P61eqz5rkj8/w8OvPqyXJT98eO4+gZ45P74rHT+ShSY/p/UmP2tXKD8yBDc/sTcqP4m8OD9epi4/H41AP7ZDKj9M2E0/IvwyP0zYTT8i/DI/Gm5PPyvpKj8TBVg/sYYrP4m8OD9epi4/qPcfP8E1NT9M2E0/IvwyPwjrWj+Ynjc/E0xrPzIkOj+bw2c/HUlMP8PLZD9ujlM/NN93P2LIWz/Dy2Q/bo5TPxNMaz8yJDo/COtaP5ieNz9M2E0/IvwyP4m8OD9epi4/qPcfP8E1NT8tnE8/BoNFP23gWD/SOkQ/m8NnPx1JTD8H1EE/WRBrP10NUD8xrFI/XQ1QPzGsUj8tnE8/BoNFP23gWD/SOkQ/m8NnPx1JTD8TBVg/sYYrPwjrWj+Ynjc/E0xrPzIkOj8I61o/mJ43PxNMaz8yJDo/5IN3P6UROD+2E3I/lfNDP7YTcj+V80M/gWZ+Pwr1VT8JyXo/MGZRP9KTeT8V3Vo/ylBcPnid0z2hVPI9QOPDPb7Eiz108IQ+uqhfPtxCMj6Ka4A+gB6FPpyRUT4Sd4Y+1G0qPjCqgj5wyh4+ws7uPkqEbj58D+0+YG9RPgCkjT6mhbs+HnOuPtR7vT6Q6cs+nyyePgyPzz781b4+oE3uPsna2z547sw+TfmtPiQpTz4GQ5o+yOpePolsmz5ArkY+TfmtPiQpTz6YepY+qDRLPolsmz5ArkY+Ht+NPoTDLz7h+KI+5Cs0PnBulT7UeEA+4fiiPuQrND5fJbg+aPvpPYpvpj4MaQc+MIKmPkin8j1uYKM+uOr9PWYZmj587AA+pBnFPrCw8z0bqrg+3CErPjCCpj5Ip/I9Y7m0Psg12T25IM8+wPj6PQvi4z5sVgw+x3XUPnDROj5rvMQ+fJUzPoeOtD7MGmE+R2O+Phwfbz5N+a0+JClPPjIBrD6MCXw+iWybPkCuRj5wbpU+1HhAPph6lj6oNEs+iWybPkCuRj7h+KI+5Cs0PjLJnT5slmY+BkOaPsjqXj7KMKA+BNV7Pk1Ipj6auYQ+MgGsPowJfD5N+a0+JClPPiGtqD4MnXE+R2O+Phwfbz7HddQ+cNE6PhuquD7cISs+a7zEPnyVMz65IM8+wPj6PQvi4z5sVgw+pBnFPrCw8z1jubQ+yDXZPV8luD5o++k9im+mPgxpBz4wgqY+SKfyPW5goz646v09MIKmPkin8j2xnpI+UGQjPh7fjT6Ewy8+UoCXPmhr1T1q35U+2KKkPXoHuD7IAp895ViiPrA1nD0Mqrk+gIt1PVdHoz4Qfmk9FqOWPnDNbT1XR6M+EH5pPeVYoj6wNZw9DKq5PoCLdT16B7g+yAKfPU1Ipj6auYQ+MgGsPowJfD5HY74+HB9vPkdjvj4cH28+xFjXPgB9bz7HddQ+cNE6Pgvi4z5sVgw+QX70PlTbPj7Owvw+zEANPlpC3j6+ioc+VFH4PthNcj5dp8k+3PGUPh0ZBD9qbI8+yEwDPyRVXz42cww/rNOCPlRR+D7YTXI+WkLePr6Khz4r/tA+QgK2PsRY1z4AfW8+IcDjPuAkQj4L4uM+bFYMPkF+9D5U2z4+zsL8PsxADT4euoo+nkYAPzQilT4i9wU/6LOuPshhBj8wWao+xmENP6hcdz5w4hY/AS6ZPnybFT8IjbQ+ioMNP11ntj4MbRY/WCrBPtYPFj9Nb0g+NFURP1/qvj5skAw/XWe2PgxtFj+hByU+0PEPP+uzrj7IYQY/qFx3PnDiFj8oRHc+cGQOPwEumT58mxU/NCKVPiL3BT8zWao+xmENPwiNtD6Kgw0/0V29PeTHGD/QcOE8/FMhP9FdvT1OXik/mCwXPlUNGj+YLBc+4BgoP6T6Sz4aEyE/qvpLPrzAGz+q+ks+eGUmP+VArD7CZPk+PLCTPv599j5N/kY/jVoFP03+Rj+NWgU/9Nk4PzCXJj46NzY/QBXuPbCUPT/YIQo+YPlCP2Bs9j3drj8/IFUwPu2mNz/YKzw+MqJJP3D63T0yokk/cPrdPfHxTD9YUjM+gFtQP0iGVz5rGlI/oAiEPkkaMD+gWDI+9Nk4PzCXJj7drj8/IFUwPrCUPT/YIQo+YPlCP2Bs9j3x8Uw/WFIzPoBbUD9Ihlc+axpSP6AIhD7vAmc/8M6IPf6daD8goSQ96Y57P8BfVz3+nWg/IKEkPe8CZz/wzog9u2lzP3SJpT499Rw/MKLWPt9+Hj/4i8Y+GOkwP/JcuD5D/T0/5AC5Pj0ZHz+sfbc+NwMzP7zbqD623SI/UMiiPng3Hz+kwpE+lncDPzQHpD40EQw/UM2ZPocgPD9AKaI+imI0PxBWnT47OCc/LC+DPmYwLD8Y2Iw+1sM0P9LOjD7XGTA/4ICBPhYlMj9Ewnk+7NkyPxCQdj4UKDQ/1IGHPtbDND/Szow+FCg0P9SBhz7s2TI/EJB2PhYlMj9Ewnk+1xkwP+CAgT47OCc/LC+DPmYwLD8Y2Iw+imI0PxBWnT43AzM/vNuoPrbdIj9QyKI+GOkwP/JcuD49GR8/rH23PnheMD8Up8A+eDcfP6TCkT6WdwM/NAekPuKV/j5kups+NBEMP1DNmT7ffh4/+IvGPt9+Hj/4i8Y+l5g/P/y6yD4/oSo/kKBXPu2mNz/YKzw+SRowP6BYMj437SI/cDYNPQOxJT/A4gM9F8MkP3A8VT0Q0Cg/IE8/PQvvKz9YKpw95C8mP+hWtT0iviE/WGuhPekbIz8I5cM9IPwTP0DfjjyWmxc/AMZXPOfMGT8AgPM8D7YRPwBKpDy/sxE/gNgUPX0rGT+AQ4E9AFYdP3BtaD2BRRs/GJ2wPajcGD9Q8rg9SBUSPwg2hD1xAxU/YJLAPd3LED+QQMY9bKMMP/jjgz0owgw/UCTFPTQwCj+wYno9YooKPxCTwD2pQAg/YNujPUWEGz9MaAw+iasYP4Q1DD7ushM/XCYLPnvAFz9IWSo+5vsUP0R/Kz5AyRo/1EktPuA3HT8QDDQ+lAIYP5BtQD76YBU/HEpBPp1HGj8k7T0+gtgcP+Q+Oj7jZxg/APNKPqZlFj9szEw+3v4ZP3CeSj7LIRw/hGNHPlPsFz/4VVs+J9MaP7SeUz5lMR4/bJxPPuUTHD/0J1c+gr8cPxz0Xj7mtR8/tOdVPvC4IT8olk0+ka8kP4ghOz79zCE/0Ek2Pkb/Ij9ITSw+SN4gP3COFT7SEyg/rEgePunMJD/A1Pg90O0SP3xXBD7ztRI/EEMSPjEsEj/YISI+SZ4MPxjw+T0TjAw/8MXNPV5eDj/sDQ0++hYKP2Dkyz1vYA4/SBUgPoSTET+4YDA+cw8LP2RSDT4gtww/LD8gPtugDz/gDkI+rlgGPxQ1BT7fdAI/YMAHPrcjAz/ErBU+UMwGP1TyEj5yhQc/vAgiPoUCBD8ooSU+OSMLP/T+Hz56PAg/DHAuPnItBT8MmjM+cEALPzyXKz781wc/YLVCPvt2Cz/4Zjo+JhYNP7QYPD5Jngw/GPD5PROMDD/wxc09rlgGPxQ1BT7fdAI/YMAHPrcjAz/ErBU+UMwGP1TyEj7Q7RI/fFcEPvO1Ej8QQxI+MSwSP9ghIj5zDws/ZFINPl5eDj/sDQ0+b2AOP0gVID6EkxE/uGAwPiC3DD8sPyA+26APP+AOQj4mFg0/tBg8PjkjCz/0/h8++3YLP/hmOj5yhQc/vAgiPoUCBD8ooSU+ci0FPwyaMz5ahQk/YAI7PvzXBz9gtUI+SZ4MPxjw+T0TjAw/8MXNPa5YBj8UNQU+33QCP2DABz62IwM/xKwVPlDMBj9U8hI+zu0SP3xXBD7ztRI/EEMSPjEsEj/YISI+cw8LP2RSDT5eXg4/7A0NPm9gDj9IFSA+hJMRP7hgMD4gtww/ND8gPqUnDj9IOSw+26APP+AOQj4mFg0/tBg8PnBACz88lys+OSMLP/T+Hz77dgs/+GY6Pno8CD8McC4+cYUHP7wIIj6FAgQ/KKElPnItBT8MmjM+WoUJP2ACOz781wc/YLVCPhOMDD/wxc09SZ4MPxjw+T3F1Q4/cNrKPTCEDj+4Mvo9zu0SP3xXBD7ztRI/EEMSPjEsEj/YISI+rlgGPxQ1BT7fdAI/YMAHPrYjAz/ErBU+Xl4OP+wNDT5zDws/ZFINPlDMBj9U8hI+cYUHP7wIIj6FAgQ/KKElPm9gDj9IFSA+hJMRP7hgMD4gtww/ND8gPtugDz/gDkI+OSMLP/T+Hz56PAg/DHAuPnItBT8MmjM+cEALPzyXKz4mFg0/tBg8Pvt2Cz/4Zjo+WoUJP2ACOz781wc/YLVCPng3Hz+kwpE+tt0iP1DIoj623SI/UMiiPng3Hz+kwpE+tt0iP1DIoj7KUFw+eJ3TPbqoXz7cQjI+imuAPoAehT6+xIs9dPCEPpyRUT4Sd4Y+YG9RPgCkjT7UbSo+MKqCPsT5pj3MvO4+cMoePsLO7j6mhbs+HnOuPp8snj4Mj88+1Hu9PpDpyz781b4+oE3uPsna2z547sw+HrqKPp5GAD80IpU+IvcFPyhEdz5wZA4/NCKVPiL3BT9Nb0g+NFURP6hcdz5w4hY/oQclPtDxDz8BLpk+fJsVPwEumT58mxU/R5ypPm7iFj/rs64+yGEGPzNZqj7GYQ0/CI20PoqDDT9dZ7Y+DG0WP1/qvj5skAw/WCrBPtYPFj/os64+yGEGPwiNtD6Kgw0/XWe2PgxtFj8wWao+xmENP9FdvT3kxxg/0HDhPPxTIT+YLBc+VQ0aP5gsFz7gGCg/wFY2PpxKKT+k+ks+GhMhP6r6Sz68wBs/qvpLPnhlJj+skaA+LkH5PqyRoD4uQfk+idG1Pv599j7ffh4/+IvGPnbBOz904+E+P6EqP5CgVz437SI/cDYNPQOxJT/A4gM9ENAoPyBPPz0L7ys/WCqcPeQvJj/oVrU9Ir4hP1hroT3pGyM/COXDPSD8Ez9A3448lpsXPwDGVzznzBk/AIDzPA+2ET8ASqQ8v7MRP4DYFD19Kxk/gEOBPQBWHT9wbWg9gUUbPxidsD2o3Bg/UPK4PUgVEj8INoQ9cQMVP2CSwD3dyxA/kEDGPWyjDD/444M9KMIMP1AkxT00MAo/sGJ6PWKKCj8Qk8A9qUAIP2Dboz1FhBs/TGgMPomrGD+ENQw+7rITP1wmCz79GBQ/LOYaPnvAFz9IWSo+5vsUP0R/Kz5AyRo/1EktPuA3HT8QDDQ+lAIYP5BtQD6dRxo/JO09PoLYHD/kPjo+42cYPwDzSj6mZRY/bMxMPt7+GT9wnko+U+wXP/hVWz4n0xo/tJ5TPsshHD+EY0c+5RMcP/QnVz6Cvxw/HPRePmUxHj9snE8+5rUfP7TnVT6RryQ/iCE7Pv3MIT/QSTY+Rv8iP0hNLD6aHyY/fI4rPkjeID9wjhU+0hMoP6xIHj7Q7RI/fFcEPvO1Ej8QQxI+MSwSP9ghIj5Jngw/GPD5PROMDD/wxc09Xl4OP+wNDT76Fgo/YOTLPW9gDj9IFSA+hJMRP7hgMD5zDws/ZFINPiC3DD8sPyA+26APP+AOQj6uWAY/FDUFPt90Aj9gwAc+tyMDP8SsFT5QzAY/VPISPnKFBz+8CCI+hQIEPyihJT45Iws/9P4fPno8CD8McC4+ci0FPwyaMz5wQAs/PJcrPvzXBz9gtUI++3YLP/hmOj4mFg0/tBg8Pq5YBj8UNQU+tyMDP8SsFT5Jngw/GPD5PROMDD/wxc09UMwGP1TyEj5zDws/ZFINPnKFBz+8CCI+hQIEPyihJT7ztRI/EEMSPjEsEj/YISI+Xl4OP+wNDT5vYA4/SBUgPoSTET+4YDA+ILcMPyw/ID45Iws/9P4fPtugDz/gDkI+ci0FPwyaMz4mFg0/tBg8Pvt2Cz/4Zjo+WoUJP2ACOz781wc/YLVCPt90Aj9gwAc+tiMDP8SsFT5Jngw/GPD5PROMDD/wxc09UMwGP1TyEj5zDws/ZFINPnGFBz+8CCI+zu0SP3xXBD7ztRI/EEMSPl5eDj/sDQ0+b2AOP0gVID6EkxE/uGAwPiC3DD80PyA+OSMLP/T+Hz6lJw4/SDksPtugDz/gDkI+cEALPzyXKz56PAg/DHAuPnItBT8MmjM+JhYNP7QYPD77dgs/+GY6PlqFCT9gAjs+/NcHP2C1Qj4TjAw/8MXNPUmeDD8Y8Pk9xdUOP3Dayj0whA4/uDL6Pc7tEj98VwQ+87USPxBDEj4xLBI/2CEiPq5YBj8UNQU+33QCP2DABz62IwM/xKwVPnMPCz9kUg0+UMwGP1TyEj5xhQc/vAgiPoUCBD8ooSU+b2AOP0gVID6EkxE/uGAwPiC3DD80PyA+OSMLP/T+Hz56PAg/DHAuPnItBT8MmjM+pScOP0g5LD7boA8/4A5CPnBACz88lys+JhYNP7QYPD77dgs/+GY6PlqFCT9gAjs+/NcHP2C1Qj54Nx8/pMKRPrbdIj9QyKI+tt0iP1DIoj64pzM/ml6iPg=="),
"bone_aabbs": [AABB(0, 0, 0, -1, -1, -1), AABB(-0.559003, 3.10092, -0.426632, 1.11801, 0.97008, 0.808933), AABB(-0.715686, 3.10092, -0.523613, 1.43137, 1.31905, 0.945827), AABB(-0.519422, 3.61788, -0.530815, 1.03884, 1.32763, 0.890348), AABB(-0.649524, 4.37489, -0.530815, 1.29905, 0.985084, 0.890348), AABB(-0.649524, 4.89484, -0.43712, 1.29905, 1.18132, 1.08381), AABB(-0.685249, 5.07236, -0.475745, 1.3705, 1.11011, 1.12244), AABB(0, 4.01998, -0.530815, 1.04729, 1.13438, 0.839635), AABB(0.751323, 3.25429, -0.182407, 0.98648, 1.09496, 0.491227), AABB(1.41654, 3.2307, -0.106165, 0.374445, 0.386343, 0.3414), AABB(1.44536, 3.10482, -0.155721, 0.316317, 0.430225, 0.193135), AABB(1.57019, 3.01716, -0.155721, 0.107072, 0.19328, 0.0769211), AABB(1.57508, 3.13143, -0.113273, 0.285937, 0.295005, 0.193219), AABB(1.75121, 3.03846, -0.0808046, 0.138369, 0.209051, 0.078407), AABB(1.77963, 2.93068, -0.0808046, 0.143522, 0.200752, 0.0706789), AABB(1.65662, 3.12248, -0.0687244, 0.229362, 0.303953, 0.191698), AABB(1.77973, 2.99543, -0.00568617, 0.151657, 0.254153, 0.0798679), AABB(1.84222, 2.88292, -0.00568617, 0.118651, 0.20104, 0.0699244), AABB(1.66431, 3.11573, 0.00221214, 0.203437, 0.323034, 0.219084), AABB(1.7643, 3.00559, 0.0737142, 0.146981, 0.239143, 0.0783868), AABB(1.79728, 2.90919, 0.0737142, 0.144987, 0.206541, 0.0738904), AABB(1.6052, 3.10839, 0.0799355, 0.239684, 0.291121, 0.1553), AABB(1.73738, 3.05349, 0.148649, 0.135307, 0.179666, 0.0757297), AABB(1.76726, 2.96879, 0.148649, 0.1362, 0.193441, 0.0730994), AABB(-1.04729, 4.01998, -0.530815, 1.04729, 1.13438, 0.890348), AABB(-1.7378, 3.25429, -0.182407, 0.98648, 1.09496, 0.491227), AABB(-1.79099, 3.2307, -0.106165, 0.374445, 0.386343, 0.3414), AABB(-1.76167, 3.10482, -0.155721, 0.316327, 0.430225, 0.193135), AABB(-1.67727, 3.01716, -0.155721, 0.107072, 0.19328, 0.0769211), AABB(-1.86102, 3.13143, -0.113273, 0.285937, 0.295005, 0.193219), AABB(-1.88958, 3.03846, -0.0808046, 0.138369, 0.209051, 0.078407), AABB(-1.92315, 2.93068, -0.0808046, 0.143522, 0.200752, 0.0706789), AABB(-1.88598, 3.12248, -0.0687244, 0.229362, 0.303953, 0.191698), AABB(-1.93138, 2.99543, -0.00568617, 0.151657, 0.254153, 0.0798679), AABB(-1.96087, 2.88292, -0.00568617, 0.118651, 0.20104, 0.0699244), AABB(-1.86774, 3.11573, 0.00221214, 0.203437, 0.323034, 0.219084), AABB(-1.91128, 3.00559, 0.0737142, 0.146981, 0.239143, 0.0783868), AABB(-1.94227, 2.90919, 0.0737142, 0.144987, 0.206541, 0.0738904), AABB(-1.84488, 3.10839, 0.0799355, 0.239684, 0.291121, 0.1553), AABB(-1.87268, 3.05349, 0.148649, 0.135317, 0.179666, 0.0757297), AABB(-1.90346, 2.96879, 0.148649, 0.1362, 0.193441, 0.0730994), AABB(-0.715686, 1.57879, -0.523613, 0.715696, 2.53494, 0.982754), AABB(-0.653369, 0.00907865, -0.123783, 0.460956, 2.19213, 0.647282), AABB(-0.649532, -0.0164901, -0.527741, 0.385139, 0.65912, 1.05124), AABB(-0.649532, -0.0164901, -0.527741, 0.372075, 0.251111, 0.861437), AABB(0, 1.57879, -0.523613, 0.715686, 2.53494, 0.982754), AABB(0.192413, 0.00907865, -0.123783, 0.460956, 2.19213, 0.647282), AABB(0.264393, -0.0164901, -0.527741, 0.385139, 0.65912, 1.05124), AABB(0.277457, -0.0164901, -0.527741, 0.372075, 0.251111, 0.861427), AABB(-0.615987, -0.00143267, 0.0011477, 0.351594, 0.644063, 0.522351), AABB(0.264393, -0.00143267, 0.0011477, 0.351594, 0.644063, 0.522351), AABB(0, 0, 0, -1, -1, -1), AABB(0, 0, 0, -1, -1, -1), AABB(1.34322, 3.25429, -0.182407, 0.394586, 0.425963, 0.555121), AABB(-1.7378, 3.25429, -0.182407, 0.394596, 0.425963, 0.555121)],
"format": 34359745559,
"index_count": 5040,
"index_data": PackedByteArray("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"),
"lods": [0.229367, PackedByteArray("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"), 0.562202, PackedByteArray("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")],
"material": SubResource("StandardMaterial3D_8maty"),
"name": "SokkyComm_CellShade_MAT",
"primitive": 3,
"skin_data": PackedByteArray("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"),
"uv_scale": Vector4(0, 0, 0, 0),
"vertex_count": 1325,
"vertex_data": PackedByteArray("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")
}]
blend_shape_mode = 0

[sub_resource type="Skin" id="Skin_4wdfr"]
resource_name = "Skin"
bind_count = 55
bind/0/name = &"Joint_Control"
bind/0/bone = -1
bind/0/pose = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 0, 0)
bind/1/name = &"Joint_Root"
bind/1/bone = -1
bind/1/pose = Transform3D(1, 2.19942e-08, -5.63964e-08, -5.63964e-08, 0.677016, -0.735968, 2.19942e-08, 0.735968, 0.677016, -7.77085e-08, -1.8815, -2.64631)
bind/2/name = &"Joint_Spine_1"
bind/2/bone = -1
bind/2/pose = Transform3D(1, -5.56993e-09, 7.56329e-09, 7.56329e-09, 0.954963, -0.296724, -5.56993e-09, 0.296724, 0.954963, 1.97999e-08, -3.41265, -1.07917)
bind/3/name = &"Joint_Spine_2"
bind/3/bone = -1
bind/3/pose = Transform3D(1, 7.03107e-09, -6.17817e-09, -6.17818e-09, 0.991697, 0.128601, 7.03107e-09, -0.128601, 0.991697, -3.29899e-08, -3.99942, 0.647906)
bind/4/name = &"Joint_Spine_3"
bind/4/bone = -1
bind/4/pose = Transform3D(1, 2.74928e-07, -2.08337e-07, -2.08337e-07, 0.962733, 0.270454, 2.74928e-07, -0.270454, 0.962733, -1.28828e-06, -4.4606, 1.3057)
bind/5/name = &"Joint_Neck"
bind/5/bone = -1
bind/5/pose = Transform3D(0.999919, -0.00365401, -0.0122063, -6.98492e-09, 0.957996, -0.286781, 0.0127415, 0.286758, 0.957919, 0.0197169, -4.8822, -1.54733)
bind/6/name = &"Joint_Head"
bind/6/bone = -1
bind/6/pose = Transform3D(1, 8.10954e-09, -6.61386e-09, -8.87164e-09, 0.975238, -0.221156, 7.53786e-09, 0.221156, 0.975239, 6.08384e-08, -5.25152, -1.19408)
bind/7/name = &"Joint_Arm_1_R"
bind/7/bone = -1
bind/7/pose = Transform3D(0.0240603, 0.166809, 0.985696, 0.612745, -0.781527, 0.117301, 0.789915, 0.601157, -0.121015, -0.858299, 3.527, -3.21934)
bind/8/name = &"Joint_Arm_2_R"
bind/8/bone = -1
bind/8/pose = Transform3D(0.198045, 0.0991015, 0.97517, 0.650196, -0.75777, -0.0550382, 0.7335, 0.644952, -0.214508, -0.73226, 2.5853, -3.338)
bind/9/name = &"Joint_Hand_R"
bind/9/bone = -1
bind/9/pose = Transform3D(0.0566122, -0.00252751, 0.998393, 0.689329, -0.723292, -0.0409182, 0.722233, 0.690538, -0.0392052, -0.166599, 1.50484, -3.49672)
bind/10/name = &"Joint_Thumb_1_R"
bind/10/bone = -1
bind/10/pose = Transform3D(0.931178, 0.254031, -0.261488, 0.186494, -0.948228, -0.257068, -0.313253, 0.19061, -0.930344, -2.33212, 2.82195, -0.215828)
bind/11/name = &"Joint_Thumb_2_R"
bind/11/bone = -1
bind/11/pose = Transform3D(0.957239, 0.258287, -0.130315, 0.261013, -0.965327, 0.00399468, -0.124765, -0.0378374, -0.991465, -2.37131, 2.64174, 0.198315)
bind/12/name = &"Joint_Pointer_1_R"
bind/12/bone = -1
bind/12/pose = Transform3D(0.871259, 0.479873, 0.103096, 0.488281, -0.86876, -0.0826912, 0.0498842, 0.122386, -0.991228, -3.10358, 2.01435, -0.517988)
bind/13/name = &"Joint_Pointer_2_R"
bind/13/bone = -1
bind/13/pose = Transform3D(0.932709, 0.357787, -0.0451885, 0.35428, -0.932474, -0.0705455, -0.0673773, 0.0497895, -0.996485, -2.82857, 2.34643, -0.0743036)
bind/14/name = &"Joint_Pointer_3_R"
bind/14/bone = -1
bind/14/pose = Transform3D(0.919709, 0.392565, -0.00530468, 0.391492, -0.918042, -0.0627122, -0.0294884, 0.0556007, -0.998018, -2.91034, 2.11373, -0.162222)
bind/15/name = &"Joint_Middle_1_R"
bind/15/bone = -1
bind/15/pose = Transform3D(0.920825, 0.389892, -0.00803207, 0.389691, -0.920745, -0.0192286, -0.0148924, 0.0145765, -0.999783, -2.91561, 2.35893, 0.0165618)
bind/16/name = &"Joint_Middle_2_R"
bind/16/bone = -1
bind/16/pose = Transform3D(0.916558, 0.39989, 0.00319792, 0.399199, -0.914443, -0.0665894, -0.023704, 0.06231, -0.997775, -2.94006, 2.18552, -0.11935)
bind/17/name = &"Joint_Middle_3_R"
bind/17/bone = -1
bind/17/pose = Transform3D(0.924418, 0.380957, -0.0179585, 0.380474, -0.924443, -0.0253877, -0.0262731, 0.0166364, -0.999516, -2.89662, 2.10143, 0.0246311)
bind/18/name = &"Joint_Ring_1_R"
bind/18/bone = -1
bind/18/pose = Transform3D(0.902797, 0.424064, 0.0716048, 0.426206, -0.904463, -0.0171492, 0.0574917, 0.0460011, -0.997285, -2.9988, 2.2411, -0.139782)
bind/19/name = &"Joint_Ring_2_R"
bind/19/bone = -1
bind/19/pose = Transform3D(0.91737, 0.396796, -0.0313917, 0.396223, -0.917866, -0.0230088, -0.037943, 0.00866982, -0.999242, -2.92712, 2.20094, 0.152755)
bind/20/name = &"Joint_Ring_3_R"
bind/20/bone = -1
bind/20/pose = Transform3D(0.931075, 0.362856, -0.0378947, 0.36191, -0.931742, -0.0296192, -0.0460555, 0.0138636, -0.998843, -2.8486, 2.16623, 0.152085)
bind/21/name = &"Joint_Pinky_1_R"
bind/21/bone = -1
bind/21/pose = Transform3D(0.91144, 0.411432, -0.000137776, 0.411188, -0.910909, -0.0341812, -0.0141886, 0.0310978, -0.999416, -2.95281, 2.29411, 0.117601)
bind/22/name = &"Joint_Pinky_2_R"
bind/22/bone = -1
bind/22/pose = Transform3D(0.917401, 0.397585, 0.0173799, 0.397905, -0.915631, -0.0573679, -0.00689489, 0.0595453, -0.998202, -2.92255, 2.22752, 0.0132236)
bind/23/name = &"Joint_Pinky_3_R"
bind/23/bone = -1
bind/23/pose = Transform3D(0.935744, 0.352397, -0.0141359, 0.351315, -0.934896, -0.050468, -0.0310002, 0.0422593, -0.998626, -2.81021, 2.26422, 0.111227)
bind/24/name = &"Joint_Arm_1_L"
bind/24/bone = -1
bind/24/pose = Transform3D(0.0240603, -0.166809, -0.985696, -0.612744, -0.781527, 0.117301, -0.789915, 0.601157, -0.121015, 0.858299, 3.52701, -3.21934)
bind/25/name = &"Joint_Arm_2_L"
bind/25/bone = -1
bind/25/pose = Transform3D(0.198045, -0.0991015, -0.975171, -0.650197, -0.75777, -0.0550382, -0.733501, 0.644952, -0.214508, 0.73226, 2.58531, -3.338)
bind/26/name = &"Joint_Hand_L"
bind/26/bone = -1
bind/26/pose = Transform3D(0.0566122, 0.0025275, -0.998393, -0.689329, -0.723292, -0.0409182, -0.722233, 0.690538, -0.0392049, 0.166599, 1.50484, -3.49672)
bind/27/name = &"Joint_Thumb_1_L"
bind/27/bone = -1
bind/27/pose = Transform3D(0.931178, -0.254031, 0.261488, -0.186494, -0.948228, -0.257068, 0.313253, 0.19061, -0.930344, 2.33212, 2.82195, -0.215829)
bind/28/name = &"Joint_Thumb_2_L"
bind/28/bone = -1
bind/28/pose = Transform3D(0.957239, -0.258287, 0.130315, -0.261013, -0.965327, 0.00399454, 0.124765, -0.0378374, -0.991465, 2.37131, 2.64174, 0.198316)
bind/29/name = &"Joint_Pointer_1_L"
bind/29/bone = -1
bind/29/pose = Transform3D(0.87126, -0.479873, -0.103096, -0.488281, -0.86876, -0.0826914, -0.0498842, 0.122386, -0.991228, 3.10359, 2.01435, -0.517988)
bind/30/name = &"Joint_Pointer_2_L"
bind/30/bone = -1
bind/30/pose = Transform3D(0.932709, -0.357787, 0.0451885, -0.35428, -0.932475, -0.0705457, 0.0673775, 0.0497894, -0.996485, 2.82857, 2.34643, -0.0743029)
bind/31/name = &"Joint_Pointer_3_L"
bind/31/bone = -1
bind/31/pose = Transform3D(0.919709, -0.392565, 0.00530469, -0.391492, -0.918042, -0.0627124, 0.0294887, 0.0556006, -0.998018, 2.91034, 2.11373, -0.162221)
bind/32/name = &"Joint_Middle_1_L"
bind/32/bone = -1
bind/32/pose = Transform3D(0.920825, -0.389892, 0.00803182, -0.389691, -0.920745, -0.0192287, 0.0148924, 0.0145765, -0.999783, 2.91561, 2.35893, 0.0165619)
bind/33/name = &"Joint_Middle_2_L"
bind/33/bone = -1
bind/33/pose = Transform3D(0.916558, -0.39989, -0.00319793, -0.399199, -0.914443, -0.0665896, 0.0237042, 0.0623099, -0.997776, 2.94006, 2.18552, -0.119349)
bind/34/name = &"Joint_Middle_3_L"
bind/34/bone = -1
bind/34/pose = Transform3D(0.924418, -0.380957, 0.0179585, -0.380475, -0.924443, -0.0253879, 0.0262733, 0.0166363, -0.999517, 2.89662, 2.10143, 0.0246318)
bind/35/name = &"Joint_Ring_1_L"
bind/35/bone = -1
bind/35/pose = Transform3D(0.902797, -0.424064, -0.0716051, -0.426207, -0.904464, -0.0171494, -0.0574917, 0.0460011, -0.997286, 2.9988, 2.2411, -0.139782)
bind/36/name = &"Joint_Ring_2_L"
bind/36/bone = -1
bind/36/pose = Transform3D(0.91737, -0.396796, 0.0313917, -0.396224, -0.917866, -0.023009, 0.0379433, 0.00866973, -0.999243, 2.92712, 2.20094, 0.152756)
bind/37/name = &"Joint_Ring_3_L"
bind/37/bone = -1
bind/37/pose = Transform3D(0.931075, -0.362856, 0.0378947, -0.36191, -0.931743, -0.0296195, 0.0460557, 0.0138635, -0.998843, 2.8486, 2.16623, 0.152085)
bind/38/name = &"Joint_Pinky_1_L"
bind/38/bone = -1
bind/38/pose = Transform3D(0.91144, -0.411432, 0.000137502, -0.411188, -0.91091, -0.0341814, 0.0141886, 0.0310978, -0.999416, 2.95281, 2.29411, 0.117601)
bind/39/name = &"Joint_Pinky_2_L"
bind/39/bone = -1
bind/39/pose = Transform3D(0.917401, -0.397585, -0.0173799, -0.397905, -0.915631, -0.0573681, 0.00689513, 0.0595452, -0.998202, 2.92255, 2.22752, 0.0132244)
bind/40/name = &"Joint_Pinky_3_L"
bind/40/bone = -1
bind/40/pose = Transform3D(0.935744, -0.352397, 0.0141359, -0.351315, -0.934896, -0.0504682, 0.0310005, 0.0422592, -0.998626, 2.81021, 2.26422, 0.111228)
bind/41/name = &"Joint_Leg_1_L"
bind/41/bone = -1
bind/41/pose = Transform3D(0.998401, -0.0536197, -0.0179041, -0.0531472, -0.998251, 0.025901, -0.0192616, -0.024908, -0.999504, 0.529158, 3.38183, 0.0691275)
bind/42/name = &"Joint_Leg_2_L"
bind/42/bone = -1
bind/42/pose = Transform3D(0.999684, -0.0132663, -0.0213678, -0.0103233, -0.991144, 0.132389, -0.0229349, -0.132126, -0.990968, 0.451321, 1.91943, 0.275845)
bind/43/name = &"Joint_Foot_L"
bind/43/bone = -1
bind/43/pose = Transform3D(1, -1.68422e-07, 2.70963e-07, 1.45203e-07, -0.521331, -0.853354, 2.9825e-07, 0.853355, -0.521331, 0.441919, 0.378312, -0.148553)
bind/44/name = &"Joint_Toes_L"
bind/44/bone = -1
bind/44/pose = Transform3D(-0.000428254, 1, 1.13692e-10, -3.68804e-07, -1.57947e-10, -1, -1, -0.000428269, 2.65479e-07, -0.107621, -0.109097, -0.441873)
bind/45/name = &"Joint_Leg_1_R"
bind/45/bone = -1
bind/45/pose = Transform3D(0.998401, 0.0536198, 0.0179041, 0.0531472, -0.998251, 0.025901, 0.0192616, -0.024908, -0.999504, -0.529158, 3.38183, 0.0691275)
bind/46/name = &"Joint_Leg_2_R"
bind/46/bone = -1
bind/46/pose = Transform3D(0.999684, 0.0132663, 0.0213679, 0.0103232, -0.991144, 0.132389, 0.0229349, -0.132126, -0.990968, -0.451321, 1.91943, 0.275845)
bind/47/name = &"Joint_Foot_R"
bind/47/bone = -1
bind/47/pose = Transform3D(1, 1.66832e-07, -2.69992e-07, -1.53301e-07, -0.521331, -0.853355, -2.74276e-07, 0.853355, -0.521331, -0.441919, 0.378312, -0.148553)
bind/48/name = &"Joint_Toes_R"
bind/48/bone = -1
bind/48/pose = Transform3D(-0.00042829, -1, 8.92801e-08, 2.41518e-07, -1.03435e-10, -1, 1, -0.000428271, 2.96291e-07, 0.107621, -0.109097, -0.441873)
bind/49/name = &"IK_Foot_L"
bind/49/bone = -1
bind/49/pose = Transform3D(-1, 8.74228e-08, 0, 0, 0, 1, 8.74228e-08, 1, 0, -0.441919, -0.245389, -0.323994)
bind/50/name = &"IK_Foot_R"
bind/50/bone = -1
bind/50/pose = Transform3D(-1, 8.74228e-08, 0, 0, 0, 1, 8.74228e-08, 1, 0, 0.441919, -0.245389, -0.323994)
bind/51/name = &"IK_Knee_L"
bind/51/bone = -1
bind/51/pose = Transform3D(1, 0, 0, 0, 1.19209e-07, -1, 0, 1, 0, 0.425037, -0.994356, -1.94487)
bind/52/name = &"IK_Knee_R"
bind/52/bone = -1
bind/52/pose = Transform3D(1, 0, 0, 0, 1.19209e-07, -1, 0, 1, 0, -0.425037, -0.994356, -1.94487)
bind/53/name = &"IK_Arm_R"
bind/53/bone = -1
bind/53/pose = Transform3D(-0.882948, 0.469472, 0, 0, 0, 1, 0.469472, 0.882948, 0, -0.322129, -0.0908179, -3.79569)
bind/54/name = &"IK_Arm_L"
bind/54/bone = -1
bind/54/pose = Transform3D(-0.882948, -0.469472, 0, 0, 0, 1, -0.469472, 0.882948, 0, 0.322129, -0.0908179, -3.79569)

[sub_resource type="Animation" id="Animation_chh1t"]
resource_name = "Action"
length = 0.0416667
tracks/0/type = "position_3d"
tracks/0/imported = true
tracks/0/enabled = true
tracks/0/path = NodePath("Rig_SokkyComm/Skeleton3D:Joint_Root")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = PackedFloat32Array(0, 1, 2.98023e-08, 2.61664, 0.406871)
tracks/1/type = "rotation_3d"
tracks/1/imported = true
tracks/1/enabled = true
tracks/1/path = NodePath("Rig_SokkyComm/Skeleton3D:Joint_Root")
tracks/1/interp = 1
tracks/1/loop_wrap = true
tracks/1/keys = PackedFloat32Array(0, 1, -0.401478, -0.0175241, 0.0399313, 0.91483)
tracks/2/type = "rotation_3d"
tracks/2/imported = true
tracks/2/enabled = true
tracks/2/path = NodePath("Rig_SokkyComm/Skeleton3D:Joint_Spine_1")
tracks/2/interp = 1
tracks/2/loop_wrap = true
tracks/2/keys = PackedFloat32Array(0, 1, 0.259074, 0.0425631, -0.0672633, 0.962572)
tracks/3/type = "rotation_3d"
tracks/3/imported = true
tracks/3/enabled = true
tracks/3/path = NodePath("Rig_SokkyComm/Skeleton3D:Joint_Spine_2")
tracks/3/interp = 1
tracks/3/loop_wrap = true
tracks/3/keys = PackedFloat32Array(0, 1, 0.0970649, -0.00245168, 0.0418513, 0.994395)
tracks/4/type = "rotation_3d"
tracks/4/imported = true
tracks/4/enabled = true
tracks/4/path = NodePath("Rig_SokkyComm/Skeleton3D:Joint_Spine_3")
tracks/4/interp = 1
tracks/4/loop_wrap = true
tracks/4/keys = PackedFloat32Array(0, 1, -0.0158004, 0.0110337, -0.0671962, 0.997554)
tracks/5/type = "rotation_3d"
tracks/5/imported = true
tracks/5/enabled = true
tracks/5/path = NodePath("Rig_SokkyComm/Skeleton3D:Joint_Neck")
tracks/5/interp = 1
tracks/5/loop_wrap = true
tracks/5/keys = PackedFloat32Array(0, 1, -0.279209, -0.0309112, 0.0707666, 0.95712)
tracks/6/type = "rotation_3d"
tracks/6/imported = true
tracks/6/enabled = true
tracks/6/path = NodePath("Rig_SokkyComm/Skeleton3D:Joint_Head")
tracks/6/interp = 1
tracks/6/loop_wrap = true
tracks/6/keys = PackedFloat32Array(0, 1, 0.123318, -0.00551235, -0.00748843, 0.992324)
tracks/7/type = "position_3d"
tracks/7/imported = true
tracks/7/enabled = true
tracks/7/path = NodePath("Rig_SokkyComm/Skeleton3D:Joint_Arm_1_R")
tracks/7/interp = 1
tracks/7/loop_wrap = true
tracks/7/keys = PackedFloat32Array(0, 1, 0.474035, 0.234863, 0.0189395)
tracks/8/type = "rotation_3d"
tracks/8/imported = true
tracks/8/enabled = true
tracks/8/path = NodePath("Rig_SokkyComm/Skeleton3D:Joint_Arm_1_R")
tracks/8/interp = 1
tracks/8/loop_wrap = true
tracks/8/keys = PackedFloat32Array(0, 1, -0.776402, -0.0842644, -0.41727, 0.464742)
tracks/9/type = "rotation_3d"
tracks/9/imported = true
tracks/9/enabled = true
tracks/9/path = NodePath("Rig_SokkyComm/Skeleton3D:Joint_Arm_2_R")
tracks/9/interp = 1
tracks/9/loop_wrap = true
tracks/9/keys = PackedFloat32Array(0, 1, 0.209549, -0.0405684, 0.493072, 0.8434)
tracks/10/type = "position_3d"
tracks/10/imported = true
tracks/10/enabled = true
tracks/10/path = NodePath("Rig_SokkyComm/Skeleton3D:Joint_Thumb_1_R")
tracks/10/interp = 1
tracks/10/loop_wrap = true
tracks/10/keys = PackedFloat32Array(0, 1, -0.158525, 0.145162, -0.0594007)
tracks/11/type = "rotation_3d"
tracks/11/imported = true
tracks/11/enabled = true
tracks/11/path = NodePath("Rig_SokkyComm/Skeleton3D:Joint_Thumb_1_R")
tracks/11/interp = 1
tracks/11/loop_wrap = true
tracks/11/keys = PackedFloat32Array(0, 1, 0.141361, -0.67182, 0.349761, 0.637449)
tracks/12/type = "rotation_3d"
tracks/12/imported = true
tracks/12/enabled = true
tracks/12/path = NodePath("Rig_SokkyComm/Skeleton3D:Joint_Thumb_2_R")
tracks/12/interp = 1
tracks/12/loop_wrap = true
tracks/12/keys = PackedFloat32Array(0, 1, -0.253726, 0.0705111, 0.0549106, 0.963139)
tracks/13/type = "rotation_3d"
tracks/13/imported = true
tracks/13/enabled = true
tracks/13/path = NodePath("Rig_SokkyComm/Skeleton3D:Joint_Pointer_1_R")
tracks/13/interp = 1
tracks/13/loop_wrap = true
tracks/13/keys = PackedFloat32Array(0, 1, -0.308253, -0.57352, 0.345539, 0.675765)
tracks/14/type = "rotation_3d"
tracks/14/imported = true
tracks/14/enabled = true
tracks/14/path = NodePath("Rig_SokkyComm/Skeleton3D:Joint_Pointer_3_R")
tracks/14/interp = 1
tracks/14/loop_wrap = true
tracks/14/keys = PackedFloat32Array(0, 1, 0.0929504, -0.132202, 0.379951, 0.91078)
tracks/15/type = "rotation_3d"
tracks/15/imported = true
tracks/15/enabled = true
tracks/15/path = NodePath("Rig_SokkyComm/Skeleton3D:Joint_Middle_1_R")
tracks/15/interp = 1
tracks/15/loop_wrap = true
tracks/15/keys = PackedFloat32Array(0, 1, -0.322455, -0.76717, 0.383586, 0.400417)
tracks/16/type = "rotation_3d"
tracks/16/imported = true
tracks/16/enabled = true
tracks/16/path = NodePath("Rig_SokkyComm/Skeleton3D:Joint_Middle_2_R")
tracks/16/interp = 1
tracks/16/loop_wrap = true
tracks/16/keys = PackedFloat32Array(0, 1, 0.174946, -0.0613455, 0.362059, 0.913534)
tracks/17/type = "rotation_3d"
tracks/17/imported = true
tracks/17/enabled = true
tracks/17/path = NodePath("Rig_SokkyComm/Skeleton3D:Joint_Middle_3_R")
tracks/17/interp = 1
tracks/17/loop_wrap = true
tracks/17/keys = PackedFloat32Array(0, 1, 0.478657, -0.313181, 0.22592, 0.788521)
tracks/18/type = "rotation_3d"
tracks/18/imported = true
tracks/18/enabled = true
tracks/18/path = NodePath("Rig_SokkyComm/Skeleton3D:Joint_Ring_1_R")
tracks/18/interp = 1
tracks/18/loop_wrap = true
tracks/18/keys = PackedFloat32Array(0, 1, -0.356875, -0.756316, 0.388078, 0.387327)
tracks/19/type = "rotation_3d"
tracks/19/imported = true
tracks/19/enabled = true
tracks/19/path = NodePath("Rig_SokkyComm/Skeleton3D:Joint_Ring_2_R")
tracks/19/interp = 1
tracks/19/loop_wrap = true
tracks/19/keys = PackedFloat32Array(0, 1, 0.178778, -0.168853, 0.352746, 0.902827)
tracks/20/type = "rotation_3d"
tracks/20/imported = true
tracks/20/enabled = true
tracks/20/path = NodePath("Rig_SokkyComm/Skeleton3D:Joint_Ring_3_R")
tracks/20/interp = 1
tracks/20/loop_wrap = true
tracks/20/keys = PackedFloat32Array(0, 1, 0.520018, -0.619126, -0.140828, 0.571342)
tracks/21/type = "rotation_3d"
tracks/21/imported = true
tracks/21/enabled = true
tracks/21/path = NodePath("Rig_SokkyComm/Skeleton3D:Joint_Pinky_1_R")
tracks/21/interp = 1
tracks/21/loop_wrap = true
tracks/21/keys = PackedFloat32Array(0, 1, -0.399629, -0.744734, 0.357499, 0.397319)
tracks/22/type = "rotation_3d"
tracks/22/imported = true
tracks/22/enabled = true
tracks/22/path = NodePath("Rig_SokkyComm/Skeleton3D:Joint_Pinky_2_R")
tracks/22/interp = 1
tracks/22/loop_wrap = true
tracks/22/keys = PackedFloat32Array(0, 1, 0.135196, -0.151259, 0.188754, 0.960841)
tracks/23/type = "position_3d"
tracks/23/imported = true
tracks/23/enabled = true
tracks/23/path = NodePath("Rig_SokkyComm/Skeleton3D:Joint_Arm_1_L")
tracks/23/interp = 1
tracks/23/loop_wrap = true
tracks/23/keys = PackedFloat32Array(0, 1, -0.411503, 0.293414, 0.0493877)
tracks/24/type = "rotation_3d"
tracks/24/imported = true
tracks/24/enabled = true
tracks/24/path = NodePath("Rig_SokkyComm/Skeleton3D:Joint_Arm_1_L")
tracks/24/interp = 1
tracks/24/loop_wrap = true
tracks/24/keys = PackedFloat32Array(0, 1, 0.647168, -0.446422, -0.617939, 0.0057814)
tracks/25/type = "rotation_3d"
tracks/25/imported = true
tracks/25/enabled = true
tracks/25/path = NodePath("Rig_SokkyComm/Skeleton3D:Joint_Arm_2_L")
tracks/25/interp = 1
tracks/25/loop_wrap = true
tracks/25/keys = PackedFloat32Array(0, 1, 0.051562, 0.0443218, -0.489536, 0.869328)
tracks/26/type = "rotation_3d"
tracks/26/imported = true
tracks/26/enabled = true
tracks/26/path = NodePath("Rig_SokkyComm/Skeleton3D:Joint_Thumb_1_L")
tracks/26/interp = 1
tracks/26/loop_wrap = true
tracks/26/keys = PackedFloat32Array(0, 1, -0.0020886, 0.77021, -0.294593, 0.565674)
tracks/27/type = "rotation_3d"
tracks/27/imported = true
tracks/27/enabled = true
tracks/27/path = NodePath("Rig_SokkyComm/Skeleton3D:Joint_Pointer_1_L")
tracks/27/interp = 1
tracks/27/loop_wrap = true
tracks/27/keys = PackedFloat32Array(0, 1, 0.0658635, 0.670424, -0.0652591, 0.736163)
tracks/28/type = "rotation_3d"
tracks/28/imported = true
tracks/28/enabled = true
tracks/28/path = NodePath("Rig_SokkyComm/Skeleton3D:Joint_Pointer_2_L")
tracks/28/interp = 1
tracks/28/loop_wrap = true
tracks/28/keys = PackedFloat32Array(0, 1, -0.0574202, 0.0540214, -0.257014, 0.963187)
tracks/29/type = "rotation_3d"
tracks/29/imported = true
tracks/29/enabled = true
tracks/29/path = NodePath("Rig_SokkyComm/Skeleton3D:Joint_Middle_1_L")
tracks/29/interp = 1
tracks/29/loop_wrap = true
tracks/29/keys = PackedFloat32Array(0, 1, -0.0702001, 0.692316, -0.0876035, 0.712808)
tracks/30/type = "rotation_3d"
tracks/30/imported = true
tracks/30/enabled = true
tracks/30/path = NodePath("Rig_SokkyComm/Skeleton3D:Joint_Middle_2_L")
tracks/30/interp = 1
tracks/30/loop_wrap = true
tracks/30/keys = PackedFloat32Array(0, 1, -0.0317851, -0.100344, -0.165835, 0.98052)
tracks/31/type = "rotation_3d"
tracks/31/imported = true
tracks/31/enabled = true
tracks/31/path = NodePath("Rig_SokkyComm/Skeleton3D:Joint_Ring_1_L")
tracks/31/interp = 1
tracks/31/loop_wrap = true
tracks/31/keys = PackedFloat32Array(0, 1, -0.141221, 0.633906, -0.0582294, 0.758175)
tracks/32/type = "rotation_3d"
tracks/32/imported = true
tracks/32/enabled = true
tracks/32/path = NodePath("Rig_SokkyComm/Skeleton3D:Joint_Ring_2_L")
tracks/32/interp = 1
tracks/32/loop_wrap = true
tracks/32/keys = PackedFloat32Array(0, 1, -0.0583798, -0.0177501, -0.187156, 0.980433)
tracks/33/type = "rotation_3d"
tracks/33/imported = true
tracks/33/enabled = true
tracks/33/path = NodePath("Rig_SokkyComm/Skeleton3D:Joint_Pinky_1_L")
tracks/33/interp = 1
tracks/33/loop_wrap = true
tracks/33/keys = PackedFloat32Array(0, 1, -0.339764, 0.433827, 0.0349121, 0.833748)
tracks/34/type = "rotation_3d"
tracks/34/imported = true
tracks/34/enabled = true
tracks/34/path = NodePath("Rig_SokkyComm/Skeleton3D:Joint_Pinky_2_L")
tracks/34/interp = 1
tracks/34/loop_wrap = true
tracks/34/keys = PackedFloat32Array(0, 1, -0.0461418, 0.110826, -0.0932682, 0.988377)
tracks/35/type = "rotation_3d"
tracks/35/imported = true
tracks/35/enabled = true
tracks/35/path = NodePath("Rig_SokkyComm/Skeleton3D:Joint_Leg_1_L")
tracks/35/interp = 1
tracks/35/loop_wrap = true
tracks/35/keys = PackedFloat32Array(0, 1, -0.9224, 0.167852, -0.0379759, 0.345776)
tracks/36/type = "rotation_3d"
tracks/36/imported = true
tracks/36/enabled = true
tracks/36/path = NodePath("Rig_SokkyComm/Skeleton3D:Joint_Leg_2_L")
tracks/36/interp = 1
tracks/36/loop_wrap = true
tracks/36/keys = PackedFloat32Array(0, 1, -0.441132, 0.00302664, -0.123176, 0.888944)
tracks/37/type = "rotation_3d"
tracks/37/imported = true
tracks/37/enabled = true
tracks/37/path = NodePath("Rig_SokkyComm/Skeleton3D:Joint_Leg_1_R")
tracks/37/interp = 1
tracks/37/loop_wrap = true
tracks/37/keys = PackedFloat32Array(0, 1, -0.661374, -0.151761, 0.0312861, 0.733877)
tracks/38/type = "rotation_3d"
tracks/38/imported = true
tracks/38/enabled = true
tracks/38/path = NodePath("Rig_SokkyComm/Skeleton3D:Joint_Leg_2_R")
tracks/38/interp = 1
tracks/38/loop_wrap = true
tracks/38/keys = PackedFloat32Array(0, 1, -0.324603, 0.000118463, 0.12222, 0.93792)
tracks/39/type = "rotation_3d"
tracks/39/imported = true
tracks/39/enabled = true
tracks/39/path = NodePath("Rig_SokkyComm/Skeleton3D:Joint_Toes_R")
tracks/39/interp = 1
tracks/39/loop_wrap = true
tracks/39/keys = PackedFloat32Array(0, 1, 0.375737, 0.613653, 0.339977, 0.605531)
tracks/40/type = "position_3d"
tracks/40/imported = true
tracks/40/enabled = true
tracks/40/path = NodePath("Rig_SokkyComm/Skeleton3D:IK_Foot_L")
tracks/40/interp = 1
tracks/40/loop_wrap = true
tracks/40/keys = PackedFloat32Array(0, 1, -0.755771, 0.501419, 1.5051)
tracks/41/type = "rotation_3d"
tracks/41/imported = true
tracks/41/enabled = true
tracks/41/path = NodePath("Rig_SokkyComm/Skeleton3D:IK_Foot_L")
tracks/41/interp = 1
tracks/41/loop_wrap = true
tracks/41/keys = PackedFloat32Array(0, 1, -0.0885502, -0.789658, -0.595666, 0.117388)
tracks/42/type = "position_3d"
tracks/42/imported = true
tracks/42/enabled = true
tracks/42/path = NodePath("Rig_SokkyComm/Skeleton3D:IK_Foot_R")
tracks/42/interp = 1
tracks/42/loop_wrap = true
tracks/42/keys = PackedFloat32Array(0, 1, 0.875167, 0.323994, -1.37673)
tracks/43/type = "rotation_3d"
tracks/43/imported = true
tracks/43/enabled = true
tracks/43/path = NodePath("Rig_SokkyComm/Skeleton3D:IK_Foot_R")
tracks/43/interp = 1
tracks/43/loop_wrap = true
tracks/43/keys = PackedFloat32Array(0, 1, 2.77037e-08, 0.773508, 0.633786, 3.38111e-08)
tracks/44/type = "position_3d"
tracks/44/imported = true
tracks/44/enabled = true
tracks/44/path = NodePath("Rig_SokkyComm/Skeleton3D:IK_Knee_L")
tracks/44/interp = 1
tracks/44/loop_wrap = true
tracks/44/keys = PackedFloat32Array(0, 1, -0.425037, 1.94487, 0.0879336)
tracks/45/type = "position_3d"
tracks/45/imported = true
tracks/45/enabled = true
tracks/45/path = NodePath("Rig_SokkyComm/Skeleton3D:IK_Knee_R")
tracks/45/interp = 1
tracks/45/loop_wrap = true
tracks/45/keys = PackedFloat32Array(0, 1, 0.452769, 1.91582, -1.01987)
tracks/46/type = "position_3d"
tracks/46/imported = true
tracks/46/enabled = true
tracks/46/path = NodePath("Rig_SokkyComm/Skeleton3D:IK_Arm_R")
tracks/46/interp = 1
tracks/46/loop_wrap = true
tracks/46/keys = PackedFloat32Array(0, 1, 0.717029, 3.73694, -1.57885)
tracks/47/type = "rotation_3d"
tracks/47/imported = true
tracks/47/enabled = true
tracks/47/path = NodePath("Rig_SokkyComm/Skeleton3D:IK_Arm_R")
tracks/47/interp = 1
tracks/47/loop_wrap = true
tracks/47/keys = PackedFloat32Array(0, 1, 0.49007, 0.0810309, 0.867849, 0.0101483)
tracks/48/type = "position_3d"
tracks/48/imported = true
tracks/48/enabled = true
tracks/48/path = NodePath("Rig_SokkyComm/Skeleton3D:IK_Arm_L")
tracks/48/interp = 1
tracks/48/loop_wrap = true
tracks/48/keys = PackedFloat32Array(0, 1, -1.40924, 3.32822, 0.10921)
tracks/49/type = "rotation_3d"
tracks/49/imported = true
tracks/49/enabled = true
tracks/49/path = NodePath("Rig_SokkyComm/Skeleton3D:IK_Arm_L")
tracks/49/interp = 1
tracks/49/loop_wrap = true
tracks/49/keys = PackedFloat32Array(0, 1, -0.106199, 0.515137, 0.850401, 0.0132127)

[sub_resource type="AnimationLibrary" id="AnimationLibrary_602i2"]
_data = {
&"Action": SubResource("Animation_chh1t")
}

[sub_resource type="SphereShape3D" id="SphereShape3D_u4rs7"]
custom_solver_bias = 1.0
margin = 0.25
radius = 0.15

[sub_resource type="ShaderMaterial" id="ShaderMaterial_gsdvn"]
render_priority = 0
shader = ExtResource("25_5hvdj")
shader_parameter/albedo = Color(0, 0, 0, 1)
shader_parameter/point_size = 0.1
shader_parameter/roughness = 0.847
shader_parameter/metallic_texture_channel = Vector4(0, 0, 0, 0)
shader_parameter/specular = 0.0
shader_parameter/metallic = 0.0
shader_parameter/emission = Color(0.234375, 0.234375, 0.234375, 1)
shader_parameter/emission_energy = 6.01
shader_parameter/normal_scale = 0.0
shader_parameter/ao_texture_channel = Vector4(0, 0, 0, 0)
shader_parameter/ao_light_affect = 0.0
shader_parameter/uv1_scale = Vector3(0, 0, 0)
shader_parameter/uv1_offset = Vector3(0, 0, 0)
shader_parameter/uv2_scale = Vector3(0, 0, 0)
shader_parameter/uv2_offset = Vector3(0, 0, 0)
shader_parameter/FOV = 80.0

[sub_resource type="BoxMesh" id="BoxMesh_h3pp8"]
material = ExtResource("25_6icka")

[node name="ss_player" type="Node3D" node_paths=PackedStringArray("View", "Camera")]
script = ExtResource("1_jcds0")
View = NodePath("View Control")
Camera = NodePath("Interpolated Camera")

[node name="HUD" type="CanvasLayer" parent="."]
layer = -1

[node name="Control" type="Control" parent="HUD"]
layout_mode = 3
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
mouse_filter = 2

[node name="TopLeft" type="Control" parent="HUD/Control"]
anchors_preset = 0
offset_right = 40.0
offset_bottom = 40.0

[node name="TopRight" type="Control" parent="HUD/Control"]
layout_mode = 1
anchors_preset = 1
anchor_left = 1.0
anchor_right = 1.0
offset_left = -40.0
offset_bottom = 40.0
grow_horizontal = 0

[node name="BottomLeft" type="Control" parent="HUD/Control"]
layout_mode = 1
anchors_preset = 2
anchor_top = 1.0
anchor_bottom = 1.0
offset_top = -40.0
offset_right = 40.0
grow_vertical = 0

[node name="BottomRight" type="Control" parent="HUD/Control"]
layout_mode = 1
anchors_preset = 3
anchor_left = 1.0
anchor_top = 1.0
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = -40.0
offset_top = -40.0
grow_horizontal = 0
grow_vertical = 0

[node name="Center" type="Control" parent="HUD/Control"]
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -20.0
offset_top = -20.0
offset_right = 20.0
offset_bottom = 20.0
grow_horizontal = 2
grow_vertical = 2
mouse_filter = 2

[node name="speedometer" type="Label" parent="HUD/Control/Center"]
modulate = Color(1, 1, 1, 0.552)
layout_mode = 1
anchors_preset = 7
anchor_left = 0.5
anchor_top = 1.0
anchor_right = 0.5
anchor_bottom = 1.0
offset_left = -20.0
offset_top = 45.0
offset_right = 20.0
offset_bottom = 68.0
grow_horizontal = 2
grow_vertical = 0
label_settings = SubResource("LabelSettings_bskcs")
horizontal_alignment = 1
script = ExtResource("2_j78vv")

[node name="AspectRatioContainer" type="AspectRatioContainer" parent="HUD/Control/Center"]
layout_mode = 0
offset_right = 40.0
offset_bottom = 40.0

[node name="crosshairTex" type="TextureRect" parent="HUD/Control/Center"]
layout_mode = 0
offset_right = 40.0
offset_bottom = 40.0
mouse_filter = 2
texture = ExtResource("3_7e61h")

[node name="crosshairLabel" type="Label" parent="HUD/Control/Center"]
visible = false
layout_mode = 0
offset_top = -12.31
offset_right = 40.0
offset_bottom = 32.69
theme_override_font_sizes/font_size = 32
text = "."
horizontal_alignment = 1
vertical_alignment = 1

[node name="ChargeIndicator" parent="HUD/Control/Center" instance=ExtResource("2_tknep")]
layout_mode = 1
offset_left = -66.0
offset_top = 25.0
offset_right = -22.0
offset_bottom = 65.0

[node name="AmmoIndicator" parent="HUD/Control/Center" instance=ExtResource("6_6icka")]
layout_mode = 1
offset_left = 16.0
offset_top = 0.0
offset_right = 175.0
offset_bottom = 40.0
rotation = 0.127812
scale = Vector2(1.41, 1.41)
empty_alpha = 0.19

[node name="transparentmask" parent="HUD/Control/Center/AmmoIndicator" index="0"]
offset_left = -34.8976
offset_top = -43.4237
offset_right = 5.10237
offset_bottom = -3.42373
rotation = 0.115988
mouse_filter = 2

[node name="HP" parent="HUD/Control/Center" instance=ExtResource("7_8kt16")]
layout_mode = 1
anchors_preset = -1
anchor_left = 0.2
anchor_top = -0.2
anchor_right = 0.2
anchor_bottom = -0.2
offset_top = 57.0
offset_bottom = 97.0
shake_intensity = 5.0
manual_player_state = NodePath("../../../../Body/PlayerState")
metadata/_edit_use_anchors_ = true

[node name="Label" type="Label" parent="HUD/Control/Center"]
layout_mode = 0
offset_left = -47.0
offset_top = 51.0
offset_right = -2.0
offset_bottom = 74.0
text = "stinger"
label_settings = SubResource("LabelSettings_ja8ns")

[node name="Label2" type="Label" parent="HUD/Control/Center"]
layout_mode = 0
offset_left = 55.0
offset_top = 52.0
offset_right = 100.0
offset_bottom = 75.0
text = "ammo"
label_settings = SubResource("LabelSettings_ja8ns")

[node name="CanvasLayer" type="CanvasLayer" parent="HUD"]
script = ExtResource("7_gsdvn")

[node name="Debug UI" type="Control" parent="." node_paths=PackedStringArray("Controls", "View", "Body", "GameInfo", "ControlsInfo", "ViewInfo", "BodyInfo")]
visible = false
layout_mode = 3
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
size_flags_stretch_ratio = 0.0
mouse_filter = 2
script = ExtResource("2_rorqd")
Controls = NodePath("../User Input")
View = NodePath("../View Control")
Body = NodePath("../Body")
GameInfo = NodePath("Game/Info")
ControlsInfo = NodePath("Controls/Info")
ViewInfo = NodePath("View Control/Info")
BodyInfo = NodePath("Body/Info")

[node name="Game" type="Control" parent="Debug UI"]
anchors_preset = 0
offset_left = 8.0
offset_right = 48.0
offset_bottom = 40.0
mouse_filter = 2

[node name="Title" type="Label" parent="Debug UI/Game"]
layout_mode = 2
offset_right = 40.0
offset_bottom = 23.0
theme_override_font_sizes/font_size = 20
text = "Game"

[node name="Info" type="Label" parent="Debug UI/Game"]
layout_mode = 2
offset_top = 28.0
offset_right = 129.0
offset_bottom = 85.0
theme_override_font_sizes/font_size = 12
text = "Rendering FPS: #
Physics Tick Rate: #
Physics Frame Time: #"

[node name="Controls" type="Control" parent="Debug UI"]
anchors_preset = 0
offset_left = 8.0
offset_top = 95.0
offset_right = 48.0
offset_bottom = 135.0
mouse_filter = 2

[node name="Title" type="Label" parent="Debug UI/Controls"]
layout_mode = 2
theme_override_font_sizes/font_size = 20
text = "Controls"

[node name="Info" type="Label" parent="Debug UI/Controls"]
layout_mode = 0
offset_top = 28.0
offset_right = 135.0
offset_bottom = 145.0
theme_override_font_sizes/font_size = 12
text = "Movement Input: (#, #)
Wish Direction: (#, #, #)
Wish Speed: #
Jump Pressed: ?
Duck Pressed: ?"

[node name="View Control" type="Control" parent="Debug UI"]
anchors_preset = 0
offset_left = 8.0
offset_top = 232.0
offset_right = 48.0
offset_bottom = 272.0
mouse_filter = 2

[node name="Title" type="Label" parent="Debug UI/View Control"]
layout_mode = 2
theme_override_font_sizes/font_size = 20
text = "View Control"

[node name="Info" type="Label" parent="Debug UI/View Control"]
layout_mode = 0
offset_top = 28.0
offset_right = 114.0
offset_bottom = 65.0
theme_override_font_sizes/font_size = 12
text = "View Angles: (#, #, #)
View Offset: #"

[node name="Body" type="Control" parent="Debug UI"]
anchors_preset = 0
offset_left = 8.0
offset_top = 309.0
offset_right = 48.0
offset_bottom = 349.0
mouse_filter = 2

[node name="Title" type="Label" parent="Debug UI/Body"]
layout_mode = 2
theme_override_font_sizes/font_size = 20
text = "Body
"

[node name="Info" type="Label" parent="Debug UI/Body"]
layout_mode = 0
offset_top = 28.0
offset_right = 59.0
offset_bottom = 65.0
theme_override_font_sizes/font_size = 12
text = "Velocity: (#, #, #)
Speed: # m/s (# u/s)
Ducking: ?
Ducked: ?"

[node name="Attribution" type="RichTextLabel" parent="Debug UI"]
layout_mode = 1
anchors_preset = 2
anchor_top = 1.0
anchor_bottom = 1.0
offset_left = 9.0
offset_top = -28.0
offset_right = 262.0
offset_bottom = -5.0
grow_vertical = 0
theme_override_font_sizes/normal_font_size = 14
bbcode_enabled = true
text = "GoldGdt Character Controller
Copyright (c) 2024 ratmarrow
"
fit_content = true

[node name="User Input" type="Node" parent="." node_paths=PackedStringArray("Body", "Move", "View")]
script = ExtResource("3_mnwgk")
Parameters = ExtResource("4_gs0bd")
Body = NodePath("../Body")
Move = NodePath("../Move Functions")
View = NodePath("../View Control")

[node name="View Control" type="Node" parent="." node_paths=PackedStringArray("Body", "horizontal_view", "vertical_view", "camera_mount")]
script = ExtResource("5_nq1ha")
Parameters = ExtResource("4_gs0bd")
Body = NodePath("../Body")
horizontal_view = NodePath("../Body/Horizontal View")
vertical_view = NodePath("../Body/Horizontal View/Vertical View")
camera_mount = NodePath("../Body/Horizontal View/Vertical View/Camera Mount")

[node name="Move Functions" type="Node" parent="." node_paths=PackedStringArray("Body")]
script = ExtResource("6_gbin3")
Parameters = ExtResource("4_gs0bd")
Body = NodePath("../Body")

[node name="Body" type="CharacterBody3D" parent="." node_paths=PackedStringArray("View", "collision_hull", "duck_timer")]
collision_layer = 2
floor_constant_speed = true
script = ExtResource("7_mapp2")
Parameters = ExtResource("4_gs0bd")
View = NodePath("../View Control")
collision_hull = NodePath("Collision Hull")
duck_timer = NodePath("Duck Timer")

[node name="Collision Hull" type="CollisionShape3D" parent="Body"]
shape = SubResource("BoxShape3D_j7pn1")

[node name="Duck Timer" type="Timer" parent="Body"]

[node name="Horizontal View" type="Node3D" parent="Body"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 0.711, 0)

[node name="Vertical View" type="Node3D" parent="Body/Horizontal View"]

[node name="Camera Mount" type="Marker3D" parent="Body/Horizontal View/Vertical View"]
visible = false
gizmo_extents = 0.5

[node name="FootstepManager" parent="Body" instance=ExtResource("10_4lxdi")]
step_interval_walk = 2.0
step_interval_run = 3.075
volume_variation_db = 0.95
pitch_variation = 0.115
footstep_sounds = Array[AudioStream]([ExtResource("9_j78vv"), ExtResource("11_7e61h")])
land_sound = ExtResource("11_j78vv")
hard_land_sound = ExtResource("17_ootft")
hard_landing_airtime_threshold = 1.6
player_path = NodePath("..")
camera_path = NodePath("../../Interpolated Camera/Arm/Arm Anchor/Camera")
shake_duration = 0.2

[node name="WallJumpModule" type="Node3D" parent="Body"]
script = ExtResource("15_7omu7")
wall_jump_force = 15.0
wall_jump_angle = 50.0

[node name="PlayerState" type="Node" parent="Body"]
unique_name_in_owner = true
script = ExtResource("35_eqdhr")
death_camera_roll = -75.0
death_sound = ExtResource("21_icefa")
death_timescale_duration = 1.8
death_audio_bus = "death"
pain_sounds = Array[AudioStream]([ExtResource("22_ja8ns"), ExtResource("23_omgdn"), ExtResource("24_8kt16"), ExtResource("25_ggent")])
health_node_path = NodePath("Health")

[node name="Health" type="Node" parent="Body/PlayerState"]
script = ExtResource("27_ggent")

[node name="PlayerModel" type="Node3D" parent="Body"]
transform = Transform3D(0.285, 0, 0, 0, 0.285, 0, 0, 0, 0.285, 0, -0.910052, 0)
script = ExtResource("21_fglin")
metadata/run = true

[node name="Rig_SokkyComm" type="Node3D" parent="Body/PlayerModel"]

[node name="Skeleton3D" type="Skeleton3D" parent="Body/PlayerModel/Rig_SokkyComm"]
bones/0/name = "Joint_Control"
bones/0/parent = -1
bones/0/rest = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 0, 0)
bones/0/enabled = true
bones/0/position = Vector3(0, 0, 0)
bones/0/rotation = Quaternion(0, 0, 0, 1)
bones/0/scale = Vector3(1, 1, 1)
bones/1/name = "Joint_Root"
bones/1/parent = 0
bones/1/rest = Transform3D(1, -5.63964e-08, 2.19943e-08, 2.19942e-08, 0.677016, 0.735968, -5.63964e-08, -0.735968, 0.677016, 2.98023e-08, 3.2214, 0.406871)
bones/1/enabled = true
bones/1/position = Vector3(2.98023e-08, 2.61664, 0.406871)
bones/1/rotation = Quaternion(-0.401478, -0.0175241, 0.0399313, 0.91483)
bones/1/scale = Vector3(1, 1, 1)
bones/2/name = "Joint_Spine_1"
bones/2/parent = 1
bones/2/rest = Transform3D(1, 4.53011e-08, -5.29002e-08, -6.57337e-08, 0.864905, -0.501936, 2.30154e-08, 0.501936, 0.864905, 5.55112e-16, 0.528443, -1.33179e-07)
bones/2/enabled = true
bones/2/position = Vector3(5.55112e-16, 0.528443, -1.33179e-07)
bones/2/rotation = Quaternion(0.259074, 0.0425631, -0.0672633, 0.962572)
bones/2/scale = Vector3(1, 1, 1)
bones/3/name = "Joint_Spine_2"
bones/3/parent = 2
bones/3/rest = Transform3D(1, -1.07292e-08, 1.52479e-08, 1.61109e-08, 0.908875, -0.417069, -9.38357e-09, 0.417069, 0.908875, 1.44329e-15, 0.492549, -7.45058e-09)
bones/3/enabled = true
bones/3/position = Vector3(1.44329e-15, 0.492549, -7.45058e-09)
bones/3/rotation = Quaternion(0.0970649, -0.00245168, 0.0418513, 0.994395)
bones/3/scale = Vector3(1, 1, 1)
bones/4/name = "Joint_Spine_3"
bones/4/parent = 3
bones/4/rest = Transform3D(1, -2.03239e-07, 2.67079e-07, 2.39675e-07, 0.989519, -0.1444, -2.34932e-07, 0.1444, 0.989519, 1.85962e-15, 0.602976, 0)
bones/4/enabled = true
bones/4/position = Vector3(1.85962e-15, 0.602976, 0)
bones/4/rotation = Quaternion(-0.0158005, 0.0110337, -0.0671962, 0.997554)
bones/4/scale = Vector3(1, 1, 1)
bones/5/name = "Joint_Neck"
bones/5/parent = 4
bones/5/rest = Transform3D(0.999919, 3.15718e-07, 0.0127413, -0.00681927, 0.844733, 0.535144, -0.0107629, -0.535187, 0.844665, -1.68754e-13, 0.491729, 8.19564e-08)
bones/5/enabled = true
bones/5/position = Vector3(-1.68754e-13, 0.491729, 8.19564e-08)
bones/5/rotation = Quaternion(-0.279209, -0.0309112, 0.0707666, 0.95712)
bones/5/scale = Vector3(1, 1, 1)
bones/6/name = "Joint_Head"
bones/6/parent = 5
bones/6/rest = Transform3D(0.999919, -0.00086404, -0.0127121, 2.24099e-09, 0.997698, -0.0678131, 0.0127415, 0.0678076, 0.997617, 1.73564e-10, 0.276262, 4.36049e-08)
bones/6/enabled = true
bones/6/position = Vector3(1.73564e-10, 0.276262, 4.36049e-08)
bones/6/rotation = Quaternion(0.123318, -0.00551235, -0.00748843, 0.992324)
bones/6/scale = Vector3(1, 1, 1)
bones/7/name = "Joint_Arm_1_R"
bones/7/parent = 4
bones/7/rest = Transform3D(0.0240603, 0.612745, 0.789915, 0.427177, -0.720678, 0.546025, 0.903848, 0.324296, -0.27909, 0.402501, 0.205716, 0.0391913)
bones/7/enabled = true
bones/7/position = Vector3(0.474035, 0.234863, 0.0189395)
bones/7/rotation = Quaternion(0.776402, 0.0842644, 0.41727, -0.464742)
bones/7/scale = Vector3(1, 1, 1)
bones/8/name = "Joint_Arm_2_R"
bones/8/parent = 7
bones/8/rest = Transform3D(0.982517, -0.16501, -0.0862072, 0.158288, 0.984166, -0.0797613, 0.0980036, 0.0647212, 0.993079, 7.63685e-08, 0.832301, -8.19564e-08)
bones/8/enabled = true
bones/8/position = Vector3(7.63685e-08, 0.832301, -8.19564e-08)
bones/8/rotation = Quaternion(0.209549, -0.0405684, 0.493072, 0.8434)
bones/8/scale = Vector3(1, 1, 1)
bones/9/name = "Joint_Hand_R"
bones/9/parent = 8
bones/9/rest = Transform3D(0.984565, 0.0249362, 0.173236, -0.0162256, 0.99854, -0.0515173, -0.174268, 0.0479113, 0.983532, -2.98023e-08, 0.899819, 1.67638e-08)
bones/9/enabled = true
bones/9/position = Vector3(-2.98023e-08, 0.899819, 1.67638e-08)
bones/9/rotation = Quaternion(0.0249615, 0.0872406, -0.0103336, 0.995821)
bones/9/scale = Vector3(1, 1, 1)
bones/10/name = "Joint_Thumb_1_R"
bones/10/parent = 9
bones/10/rest = Transform3D(-0.208994, -0.243701, -0.947065, 0.468849, 0.82492, -0.315734, 0.858197, -0.510017, -0.0581445, -0.170689, 0.202219, -0.0686074)
bones/10/enabled = true
bones/10/position = Vector3(-0.158525, 0.145162, -0.0594007)
bones/10/rotation = Quaternion(0.141361, -0.67182, 0.349761, 0.637449)
bones/10/scale = Vector3(1, 1, 1)
bones/11/name = "Joint_Thumb_2_R"
bones/11/parent = 10
bones/11/rest = Transform3D(0.991048, -0.00321806, 0.133466, -0.0328956, 0.963, 0.267485, -0.129389, -0.26948, 0.954274, -1.11759e-08, 0.146907, 7.45058e-09)
bones/11/enabled = true
bones/11/position = Vector3(-1.11759e-08, 0.146907, 7.45058e-09)
bones/11/rotation = Quaternion(-0.253726, 0.0705111, 0.0549106, 0.963139)
bones/11/scale = Vector3(1, 1, 1)
bones/12/name = "Joint_Pointer_1_R"
bones/12/parent = 9
bones/12/rest = Transform3D(0.151041, -0.0527202, -0.987121, 0.249277, 0.968337, -0.0135745, 0.956581, -0.244017, 0.159401, -0.102948, 0.32089, 0.0462182)
bones/12/enabled = true
bones/12/position = Vector3(-0.102948, 0.32089, 0.0462182)
bones/12/rotation = Quaternion(-0.308253, -0.57352, 0.345539, 0.675765)
bones/12/scale = Vector3(1, 1, 1)
bones/13/name = "Joint_Pointer_2_R"
bones/13/parent = 12
bones/13/rest = Transform3D(0.979665, -0.146073, -0.137544, 0.148329, 0.988918, 0.00624668, 0.135107, -0.0265215, 0.990476, -1.48313e-07, 0.113952, 1.86265e-08)
bones/13/enabled = true
bones/13/position = Vector3(-1.48313e-07, 0.113952, 1.86265e-08)
bones/13/rotation = Quaternion(-0.00823429, -0.0685144, 0.0739801, 0.994869)
bones/13/scale = Vector3(1, 1, 1)
bones/14/name = "Joint_Pointer_3_R"
bones/14/parent = 13
bones/14/rest = Transform3D(0.998515, 0.0395183, 0.0374878, -0.0398489, 0.999173, 0.00811262, -0.0371362, -0.00959442, 0.999264, -1.36904e-07, 0.119785, 7.45058e-09)
bones/14/enabled = true
bones/14/position = Vector3(-1.36904e-07, 0.119785, 7.45058e-09)
bones/14/rotation = Quaternion(0.0929505, -0.132202, 0.379951, 0.91078)
bones/14/scale = Vector3(1, 1, 1)
bones/15/name = "Joint_Middle_1_R"
bones/15/parent = 9
bones/15/rest = Transform3D(0.0431255, 0.00519048, -0.999056, 0.353074, 0.93538, 0.0201005, 0.934601, -0.353608, 0.0385061, -0.0365588, 0.327441, 0.0617166)
bones/15/enabled = true
bones/15/position = Vector3(-0.0365588, 0.327441, 0.0617166)
bones/15/rotation = Quaternion(-0.322455, -0.76717, 0.383586, 0.400417)
bones/15/scale = Vector3(1, 1, 1)
bones/16/name = "Joint_Middle_2_R"
bones/16/parent = 15
bones/16/rest = Transform3D(0.999878, 0.0115933, 0.0104809, -0.0110839, 0.998813, -0.0474227, -0.0110183, 0.0473008, 0.99882, -6.87724e-08, 0.137753, 5.58794e-09)
bones/16/enabled = true
bones/16/position = Vector3(-6.87724e-08, 0.137753, 5.58794e-09)
bones/16/rotation = Quaternion(0.174946, -0.0613455, 0.362059, 0.913534)
bones/16/scale = Vector3(1, 1, 1)
bones/17/name = "Joint_Middle_3_R"
bones/17/parent = 16
bones/17/rest = Transform3D(0.999566, -0.0210295, -0.0206247, 0.0218591, 0.998926, 0.0408562, 0.0197434, -0.0412893, 0.998952, 2.28465e-08, 0.148659, -5.58794e-09)
bones/17/enabled = true
bones/17/position = Vector3(2.28465e-08, 0.148659, -5.58794e-09)
bones/17/rotation = Quaternion(0.478657, -0.313181, 0.22592, 0.788521)
bones/17/scale = Vector3(1, 1, 1)
bones/18/name = "Joint_Ring_1_R"
bones/18/parent = 9
bones/18/rest = Transform3D(0.121528, 0.00929254, -0.992545, 0.312672, 0.94869, 0.0471657, 0.942055, -0.316073, 0.112387, 0.0382732, 0.322966, 0.0523791)
bones/18/enabled = true
bones/18/position = Vector3(0.0382732, 0.322966, 0.0523791)
bones/18/rotation = Quaternion(-0.356875, -0.756316, 0.388078, 0.387327)
bones/18/scale = Vector3(1, 1, 1)
bones/19/name = "Joint_Ring_2_R"
bones/19/parent = 18
bones/19/rest = Transform3D(0.994218, -0.0331718, -0.102129, 0.0326398, 0.999444, -0.00687658, 0.1023, 0.00350335, 0.994748, -1.49012e-08, 0.137975, 0)
bones/19/enabled = true
bones/19/position = Vector3(-1.49012e-08, 0.137975, 0)
bones/19/rotation = Quaternion(0.178778, -0.168853, 0.352746, 0.902827)
bones/19/scale = Vector3(1, 1, 1)
bones/20/name = "Joint_Ring_3_R"
bones/20/parent = 19
bones/20/rest = Transform3D(0.999309, -0.0367766, -0.00539379, 0.0367329, 0.999293, -0.00799076, 0.00568385, 0.0077871, 0.999954, -3.44124e-07, 0.142093, 3.72529e-09)
bones/20/enabled = true
bones/20/position = Vector3(-3.44124e-07, 0.142093, 3.72529e-09)
bones/20/rotation = Quaternion(0.520019, -0.619126, -0.140828, 0.571342)
bones/20/scale = Vector3(1, 1, 1)
bones/21/name = "Joint_Pinky_1_R"
bones/21/parent = 9
bones/21/rest = Transform3D(0.0504215, -0.00854595, -0.998691, 0.330702, 0.943696, 0.00862099, 0.942388, -0.330704, 0.0504088, 0.119339, 0.315386, 0.0387185)
bones/21/enabled = true
bones/21/position = Vector3(0.119339, 0.315386, 0.0387185)
bones/21/rotation = Quaternion(-0.399629, -0.744734, 0.357499, 0.397319)
bones/21/scale = Vector3(1, 1, 1)
bones/22/name = "Joint_Pinky_2_R"
bones/22/parent = 21
bones/22/rest = Transform3D(0.999733, -0.0140456, 0.0183518, 0.0144656, 0.999632, -0.0229555, -0.0180226, 0.0232148, 0.999568, 4.13111e-08, 0.109983, -5.58794e-09)
bones/22/enabled = true
bones/22/position = Vector3(4.13111e-08, 0.109983, -5.58794e-09)
bones/22/rotation = Quaternion(0.135196, -0.151259, 0.188754, 0.960841)
bones/22/scale = Vector3(1, 1, 1)
bones/23/name = "Joint_Pinky_3_R"
bones/23/parent = 22
bones/23/rest = Transform3D(0.998314, -0.0502814, -0.0289943, 0.0504829, 0.998705, 0.00626028, 0.028642, -0.00771345, 0.99956, 4.03961e-07, 0.107407, 3.1665e-08)
bones/23/enabled = true
bones/23/position = Vector3(4.03961e-07, 0.107407, 3.1665e-08)
bones/23/rotation = Quaternion(-0.00349493, -0.0144152, 0.0252019, 0.999572)
bones/23/scale = Vector3(1, 1, 1)
bones/24/name = "Joint_Arm_1_L"
bones/24/parent = 4
bones/24/rest = Transform3D(0.0240605, -0.612745, -0.789915, -0.427177, -0.720677, 0.546025, -0.903848, 0.324296, -0.279091, -0.402501, 0.205716, 0.039191)
bones/24/enabled = true
bones/24/position = Vector3(-0.411503, 0.293414, 0.0493877)
bones/24/rotation = Quaternion(0.647168, -0.446422, -0.617939, 0.0057814)
bones/24/scale = Vector3(1, 1, 1)
bones/25/name = "Joint_Arm_2_L"
bones/25/parent = 24
bones/25/rest = Transform3D(0.982517, 0.16501, 0.0862072, -0.158288, 0.984166, -0.0797613, -0.0980036, 0.0647212, 0.993079, 7.45058e-09, 0.832301, -4.04194e-07)
bones/25/enabled = true
bones/25/position = Vector3(7.45058e-09, 0.832301, -4.04194e-07)
bones/25/rotation = Quaternion(0.051562, 0.0443218, -0.489536, 0.869328)
bones/25/scale = Vector3(1, 1, 1)
bones/26/name = "Joint_Hand_L"
bones/26/parent = 25
bones/26/rest = Transform3D(0.984565, -0.0249362, -0.173236, 0.0162256, 0.99854, -0.0515174, 0.174268, 0.0479114, 0.983532, 0, 0.899819, 3.35276e-08)
bones/26/enabled = true
bones/26/position = Vector3(0, 0.899819, 3.35276e-08)
bones/26/rotation = Quaternion(0.0249615, -0.0872406, 0.0103336, 0.995821)
bones/26/scale = Vector3(1, 1, 1)
bones/27/name = "Joint_Thumb_1_L"
bones/27/parent = 26
bones/27/rest = Transform3D(-0.208994, 0.243701, 0.947065, -0.468849, 0.82492, -0.315733, -0.858197, -0.510017, -0.0581444, 0.170689, 0.20222, -0.0686075)
bones/27/enabled = true
bones/27/position = Vector3(0.170689, 0.20222, -0.0686075)
bones/27/rotation = Quaternion(-0.00208859, 0.77021, -0.294593, 0.565674)
bones/27/scale = Vector3(1, 1, 1)
bones/28/name = "Joint_Thumb_2_L"
bones/28/parent = 27
bones/28/rest = Transform3D(0.991048, 0.0032178, -0.133466, 0.0328958, 0.963, 0.267485, 0.129388, -0.269481, 0.954274, -3.35276e-08, 0.146907, 5.21541e-08)
bones/28/enabled = true
bones/28/position = Vector3(-3.35276e-08, 0.146907, 5.21541e-08)
bones/28/rotation = Quaternion(-0.135807, -0.0664798, 0.00750602, 0.988474)
bones/28/scale = Vector3(1, 1, 1)
bones/29/name = "Joint_Pointer_1_L"
bones/29/parent = 26
bones/29/rest = Transform3D(0.151041, 0.0527202, 0.987121, -0.249277, 0.968337, -0.0135746, -0.956581, -0.244017, 0.159401, 0.102948, 0.32089, 0.046218)
bones/29/enabled = true
bones/29/position = Vector3(0.102948, 0.32089, 0.046218)
bones/29/rotation = Quaternion(0.0658635, 0.670424, -0.0652591, 0.736163)
bones/29/scale = Vector3(1, 1, 1)
bones/30/name = "Joint_Pointer_2_L"
bones/30/parent = 29
bones/30/rest = Transform3D(0.979666, 0.146073, 0.137544, -0.148329, 0.988918, 0.00624666, -0.135108, -0.0265215, 0.990476, 2.78465e-07, 0.113952, -7.07805e-08)
bones/30/enabled = true
bones/30/position = Vector3(2.78465e-07, 0.113952, -7.07805e-08)
bones/30/rotation = Quaternion(-0.0574202, 0.0540214, -0.257014, 0.963187)
bones/30/scale = Vector3(1, 1, 1)
bones/31/name = "Joint_Pointer_3_L"
bones/31/parent = 30
bones/31/rest = Transform3D(0.998515, -0.0395183, -0.0374878, 0.0398488, 0.999173, 0.00811262, 0.0371362, -0.00959442, 0.999264, 3.7509e-07, 0.119786, 1.49012e-08)
bones/31/enabled = true
bones/31/position = Vector3(3.7509e-07, 0.119786, 1.49012e-08)
bones/31/rotation = Quaternion(-0.00442845, -0.0186631, 0.0198493, 0.999619)
bones/31/scale = Vector3(1, 1, 1)
bones/32/name = "Joint_Middle_1_L"
bones/32/parent = 26
bones/32/rest = Transform3D(0.0431255, -0.00519057, 0.999056, -0.353074, 0.935379, 0.0201006, -0.934601, -0.353608, 0.0385061, 0.0365588, 0.327441, 0.0617165)
bones/32/enabled = true
bones/32/position = Vector3(0.0365588, 0.327441, 0.0617165)
bones/32/rotation = Quaternion(-0.0702001, 0.692316, -0.0876035, 0.712808)
bones/32/scale = Vector3(1, 1, 1)
bones/33/name = "Joint_Middle_2_L"
bones/33/parent = 32
bones/33/rest = Transform3D(0.999878, -0.0115935, -0.0104807, 0.0110841, 0.998813, -0.0474228, 0.011018, 0.0473008, 0.99882, -5.92845e-08, 0.137753, 1.86265e-09)
bones/33/enabled = true
bones/33/position = Vector3(-5.92845e-08, 0.137753, 1.86265e-09)
bones/33/rotation = Quaternion(-0.0317851, -0.100344, -0.165835, 0.98052)
bones/33/scale = Vector3(1, 1, 1)
bones/34/name = "Joint_Middle_3_L"
bones/34/parent = 33
bones/34/rest = Transform3D(0.999566, 0.0210296, 0.0206247, -0.0218591, 0.998926, 0.0408562, -0.0197434, -0.0412893, 0.998952, -1.42209e-07, 0.148659, 9.31323e-09)
bones/34/enabled = true
bones/34/position = Vector3(-1.42209e-07, 0.148659, 9.31323e-09)
bones/34/rotation = Quaternion(-0.0205429, 0.0100952, -0.0107256, 0.99968)
bones/34/scale = Vector3(1, 1, 1)
bones/35/name = "Joint_Ring_1_L"
bones/35/parent = 26
bones/35/rest = Transform3D(0.121528, -0.00929251, 0.992545, -0.312672, 0.948689, 0.0471657, -0.942055, -0.316073, 0.112387, -0.0382732, 0.322966, 0.052379)
bones/35/enabled = true
bones/35/position = Vector3(-0.0382732, 0.322966, 0.052379)
bones/35/rotation = Quaternion(-0.141221, 0.633906, -0.0582294, 0.758175)
bones/35/scale = Vector3(1, 1, 1)
bones/36/name = "Joint_Ring_2_L"
bones/36/parent = 35
bones/36/rest = Transform3D(0.994218, 0.0331717, 0.102129, -0.0326397, 0.999444, -0.0068766, -0.102301, 0.00350336, 0.994747, -1.37836e-07, 0.137976, -2.23517e-08)
bones/36/enabled = true
bones/36/position = Vector3(-1.37836e-07, 0.137976, -2.23517e-08)
bones/36/rotation = Quaternion(-0.0583798, -0.0177501, -0.187156, 0.980433)
bones/36/scale = Vector3(1, 1, 1)
bones/37/name = "Joint_Ring_3_L"
bones/37/parent = 36
bones/37/rest = Transform3D(0.999309, 0.0367765, 0.00539376, -0.0367328, 0.999293, -0.00799077, -0.00568382, 0.00778711, 0.999954, 3.48547e-07, 0.142093, 5.58794e-09)
bones/37/enabled = true
bones/37/position = Vector3(3.48547e-07, 0.142093, 5.58794e-09)
bones/37/rotation = Quaternion(0.00394518, 0.0027699, -0.0183807, 0.999819)
bones/37/scale = Vector3(1, 1, 1)
bones/38/name = "Joint_Pinky_1_L"
bones/38/parent = 26
bones/38/rest = Transform3D(0.0504215, 0.00854598, 0.998691, -0.330702, 0.943696, 0.00862095, -0.942387, -0.330704, 0.0504087, -0.119339, 0.315386, 0.0387185)
bones/38/enabled = true
bones/38/position = Vector3(-0.119339, 0.315386, 0.0387185)
bones/38/rotation = Quaternion(-0.339764, 0.433827, 0.0349121, 0.833748)
bones/38/scale = Vector3(1, 1, 1)
bones/39/name = "Joint_Pinky_2_L"
bones/39/parent = 38
bones/39/rest = Transform3D(0.999733, 0.0140455, -0.0183516, -0.0144655, 0.999632, -0.0229555, 0.0180224, 0.0232148, 0.999568, -2.13016e-07, 0.109983, 5.58794e-09)
bones/39/enabled = true
bones/39/position = Vector3(-2.13016e-07, 0.109983, 5.58794e-09)
bones/39/rotation = Quaternion(-0.0461418, 0.110826, -0.0932682, 0.988377)
bones/39/scale = Vector3(1, 1, 1)
bones/40/name = "Joint_Pinky_3_L"
bones/40/parent = 39
bones/40/rest = Transform3D(0.998314, 0.0502813, 0.0289943, -0.0504828, 0.998705, 0.00626025, -0.028642, -0.00771341, 0.99956, -4.70318e-08, 0.107407, -3.72529e-08)
bones/40/enabled = true
bones/40/position = Vector3(-4.70318e-08, 0.107407, -3.72529e-08)
bones/40/rotation = Quaternion(-0.00349491, 0.0144152, -0.0252018, 0.999572)
bones/40/scale = Vector3(1, 1, 1)
bones/41/name = "Joint_Leg_1_L"
bones/41/parent = 1
bones/41/rest = Transform3D(0.998401, -0.0531472, -0.0192615, -0.0231246, -0.694894, 0.71874, -0.0515838, -0.717145, -0.695012, -0.347245, 0.431071, -0.145701)
bones/41/enabled = true
bones/41/position = Vector3(-0.347245, 0.431071, -0.145701)
bones/41/rotation = Quaternion(0.9224, -0.167852, 0.0379759, -0.345776)
bones/41/scale = Vector3(1, 1, 1)
bones/42/name = "Joint_Leg_2_L"
bones/42/parent = 41
bones/42/rest = Transform3D(0.999179, 0.0404678, 0.00192871, -0.0404407, 0.993388, 0.107447, 0.00243219, -0.107437, 0.994209, -6.98492e-10, 1.4637, -3.72529e-09)
bones/42/enabled = true
bones/42/position = Vector3(-6.98492e-10, 1.4637, -3.72529e-09)
bones/42/rotation = Quaternion(-0.441132, 0.00302664, -0.123176, 0.888944)
bones/42/scale = Vector3(1, 1, 1)
bones/43/name = "Joint_Foot_L"
bones/43/parent = 42
bones/43/rest = Transform3D(0.999684, 0.0251506, -0.000180867, -0.010323, 0.40374, -0.914816, -0.0229352, 0.914528, 0.403872, -1.49012e-08, 1.63536, -4.47035e-08)
bones/43/enabled = true
bones/43/position = Vector3(-1.49012e-08, 1.63536, -4.47035e-08)
bones/43/rotation = Quaternion(0.54591, 0.00679031, -0.010586, 0.837749)
bones/43/scale = Vector3(1, 1, 1)
bones/44/name = "Joint_Toes_L"
bones/44/parent = 43
bones/44/rest = Transform3D(-0.000428438, -5.96046e-07, -1, -0.521331, 0.853354, 0.00022285, 0.853354, 0.521331, -0.00036592, 4.1752e-08, 0.415402, -1.2593e-08)
bones/44/enabled = true
bones/44/position = Vector3(4.1752e-08, 0.415402, -1.2593e-08)
bones/44/rotation = Quaternion(0.191431, -0.680836, -0.191512, 0.680544)
bones/44/scale = Vector3(1, 1, 1)
bones/45/name = "Joint_Leg_1_R"
bones/45/parent = 1
bones/45/rest = Transform3D(0.998401, 0.0531472, 0.0192616, 0.0231245, -0.694894, 0.71874, 0.0515838, -0.717145, -0.695012, 0.347245, 0.431071, -0.145701)
bones/45/enabled = true
bones/45/position = Vector3(0.347245, 0.431071, -0.145701)
bones/45/rotation = Quaternion(0.661374, 0.151761, -0.0312861, -0.733877)
bones/45/scale = Vector3(1, 1, 1)
bones/46/name = "Joint_Leg_2_R"
bones/46/parent = 45
bones/46/rest = Transform3D(0.999179, -0.0404679, -0.0019287, 0.0404407, 0.993388, 0.107447, -0.0024322, -0.107437, 0.994209, -3.73111e-08, 1.4637, 8.3819e-09)
bones/46/enabled = true
bones/46/position = Vector3(-3.73111e-08, 1.4637, 8.3819e-09)
bones/46/rotation = Quaternion(-0.324603, 0.000118463, 0.12222, 0.93792)
bones/46/scale = Vector3(1, 1, 1)
bones/47/name = "Joint_Foot_R"
bones/47/parent = 46
bones/47/rest = Transform3D(0.999684, -0.0251507, 0.000180879, 0.0103231, 0.40374, -0.914816, 0.0229352, 0.914528, 0.403872, -4.56348e-08, 1.63536, -4.09782e-08)
bones/47/enabled = true
bones/47/position = Vector3(-4.56348e-08, 1.63536, -4.09782e-08)
bones/47/rotation = Quaternion(0.54591, -0.00679031, 0.010586, 0.837749)
bones/47/scale = Vector3(1, 1, 1)
bones/48/name = "Joint_Toes_R"
bones/48/parent = 47
bones/48/rest = Transform3D(-0.000428319, 5.36429e-07, 1, 0.521331, 0.853354, 0.000222838, -0.853354, 0.521331, -0.000365787, -1.22263e-08, 0.415402, 1.7268e-09)
bones/48/enabled = true
bones/48/position = Vector3(-1.22263e-08, 0.415402, 1.7268e-09)
bones/48/rotation = Quaternion(0.375737, 0.613653, 0.339977, 0.605531)
bones/48/scale = Vector3(1, 1, 1)
bones/49/name = "IK_Foot_L"
bones/49/parent = 0
bones/49/rest = Transform3D(-1, 0, 8.74228e-08, 8.74228e-08, 0, 1, 0, 1, 0, -0.441919, 0.323994, 0.245389)
bones/49/enabled = true
bones/49/position = Vector3(-0.755771, 0.501419, 1.5051)
bones/49/rotation = Quaternion(0.0885502, 0.789658, 0.595666, -0.117388)
bones/49/scale = Vector3(1, 1, 1)
bones/50/name = "IK_Foot_R"
bones/50/parent = 0
bones/50/rest = Transform3D(-1, 0, 8.74228e-08, 8.74228e-08, 0, 1, 0, 1, 0, 0.441919, 0.323994, 0.245389)
bones/50/enabled = true
bones/50/position = Vector3(0.875167, 0.323994, -1.37673)
bones/50/rotation = Quaternion(2.77037e-08, 0.773509, 0.633786, 3.38111e-08)
bones/50/scale = Vector3(1, 1, 1)
bones/51/name = "IK_Knee_L"
bones/51/parent = 0
bones/51/rest = Transform3D(1, 0, 0, 0, 1.78814e-07, 1, 0, -1, 1.78814e-07, -0.425037, 1.94487, -0.994356)
bones/51/enabled = true
bones/51/position = Vector3(-0.425037, 1.94487, 0.0879336)
bones/51/rotation = Quaternion(-0.707107, 0, 0, 0.707107)
bones/51/scale = Vector3(1, 1, 1)
bones/52/name = "IK_Knee_R"
bones/52/parent = 0
bones/52/rest = Transform3D(1, 0, 0, 0, 1.78814e-07, 1, 0, -1, 1.78814e-07, 0.425037, 1.94487, -0.994356)
bones/52/enabled = true
bones/52/position = Vector3(0.452769, 1.91582, -1.01987)
bones/52/rotation = Quaternion(-0.707107, 0, 0, 0.707107)
bones/52/scale = Vector3(1, 1, 1)
bones/53/name = "IK_Arm_R"
bones/53/parent = 0
bones/53/rest = Transform3D(-0.882948, -4.94145e-08, 0.469472, 0.469472, -9.29351e-08, 0.882948, 0, 1, 1.05256e-07, 1.49755, 3.50263, 0.0908179)
bones/53/enabled = true
bones/53/position = Vector3(0.717029, 3.73694, -1.57885)
bones/53/rotation = Quaternion(0.49007, 0.081031, 0.867849, 0.0101483)
bones/53/scale = Vector3(1, 1, 1)
bones/54/name = "IK_Arm_L"
bones/54/parent = 0
bones/54/rest = Transform3D(-0.882948, 4.94145e-08, -0.469472, -0.469472, -9.29351e-08, 0.882948, 0, 1, 1.05256e-07, -1.49755, 3.50263, 0.0908179)
bones/54/enabled = true
bones/54/position = Vector3(-1.40924, 3.32822, 0.10921)
bones/54/rotation = Quaternion(-0.106199, 0.515137, 0.850401, 0.0132127)
bones/54/scale = Vector3(1, 1, 1)

[node name="SokkyComm" type="MeshInstance3D" parent="Body/PlayerModel/Rig_SokkyComm/Skeleton3D"]
layers = 2
mesh = SubResource("ArrayMesh_hwnqk")
skin = SubResource("Skin_4wdfr")
surface_material_override/0 = ExtResource("21_6icka")

[node name="AnimationPlayer" type="AnimationPlayer" parent="Body/PlayerModel"]
libraries = {
&"": SubResource("AnimationLibrary_602i2")
}

[node name="compass" parent="Body" instance=ExtResource("27_ouq8c")]
transform = Transform3D(-3.95636e-15, 7.54979e-08, 1, -1, -4.37114e-08, -6.56243e-16, 4.37114e-08, -1, 7.54979e-08, -0.000407696, 0.90691, 0.0113742)
layers = 2
skeleton = NodePath("../../Interpolated Camera/Arm/Arm Anchor/Camera")

[node name="Interpolated Camera" type="Node3D" parent="." node_paths=PackedStringArray("target", "camera_arm", "camera_anchor", "camera")]
script = ExtResource("8_3kmgh")
Parameters = ExtResource("4_gs0bd")
target = NodePath("../Body/Horizontal View/Vertical View/Camera Mount")
camera_arm = NodePath("Arm")
camera_anchor = NodePath("Arm/Arm Anchor")
camera = NodePath("Arm/Arm Anchor/Camera")

[node name="Arm" type="SpringArm3D" parent="Interpolated Camera"]
shape = SubResource("SphereShape3D_u4rs7")
spring_length = 0.0
margin = 0.1

[node name="Arm Anchor" type="Node3D" parent="Interpolated Camera/Arm"]
editor_description = "
"

[node name="Camera" type="Camera3D" parent="Interpolated Camera/Arm/Arm Anchor"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -0.000407696, -0.0071907, 0.0215712)
cull_mask = 4294967293
near = 0.001
script = ExtResource("16_03rbn")

[node name="Hook Raycast" type="RayCast3D" parent="Interpolated Camera/Arm/Arm Anchor/Camera"]

[node name="Gun Raycast" type="RayCast3D" parent="Interpolated Camera/Arm/Arm Anchor/Camera"]

[node name="v_revolver" parent="Interpolated Camera/Arm/Arm Anchor/Camera" instance=ExtResource("10_vqa1p")]
transform = Transform3D(-5.29708e-09, 6.14311e-10, -0.121971, 0.0165006, 0.120849, -1.07946e-10, 0.120849, -0.0165006, -5.33152e-09, -0.0257171, 0.101102, 0.526343)

[node name="gunframe_001" parent="Interpolated Camera/Arm/Arm Anchor/Camera/v_revolver" index="0"]
transform = Transform3D(1, -5.68434e-14, 0, -2.11758e-22, 1, 7.10543e-15, 3.55271e-15, 2.96372e-16, 1, -8.7863, -1.09293, -2.41932)
layers = 4
surface_material_override/0 = SubResource("ShaderMaterial_gsdvn")

[node name="GunStart" type="Marker3D" parent="Interpolated Camera/Arm/Arm Anchor/Camera/v_revolver/gunframe_001" index="0"]
transform = Transform3D(0.193021, 0.476234, 3.81445, -0.350946, 0.669893, -2.32394, -0.924805, -0.154815, 1.67802, -1.78487, 0.405431, 0.0175371)

[node name="hand_template_001" parent="Interpolated Camera/Arm/Arm Anchor/Camera/v_revolver" index="1"]
transform = Transform3D(-0.370891, -0.224145, -0.90122, 0.749999, 0.5, -0.433014, 0.547668, -0.836516, -0.0173368, -8.09702, -1.57814, -2.28366)
layers = 4

[node name="v_hook_arm" type="MeshInstance3D" parent="Interpolated Camera/Arm/Arm Anchor/Camera/v_revolver"]
transform = Transform3D(0.193021, 0.476234, 3.81445, -0.350946, 0.669893, -2.32394, -0.924805, -0.154815, 1.67802, -7.74509, -2.72682, 2.70959)
layers = 4
mesh = SubResource("BoxMesh_h3pp8")
skeleton = NodePath("../..")
surface_material_override/0 = ExtResource("25_6icka")

[node name="HookRopeStart" type="Marker3D" parent="Interpolated Camera/Arm/Arm Anchor/Camera/v_revolver/v_hook_arm"]
transform = Transform3D(1, -2.98023e-08, 0, -1.49012e-08, 1, -2.38419e-07, 1.11759e-08, -7.45058e-09, 1, -0.235838, 0.00133324, -0.719922)

[node name="GunJump" type="Node3D" parent="."]
script = ExtResource("23_yi2sc")
enabled = false

[node name="Kickback" type="Node3D" parent="."]
script = ExtResource("24_v3lrb")
wallKickbackMultiplier = 3.6
airDodgeSound = ExtResource("35_5hvdj")
fovDodgeChange = 6.0
fovReturnDuration = 0.3
kickbackSound = ExtResource("45_ja8ns")
audio_bus = "sfx"

[node name="GamepadController" type="Node" parent="."]

[node name="WeaponSystem" parent="." node_paths=PackedStringArray("shot_raycast", "animation_player", "gun_start_marker") instance=ExtResource("36_7tjvu")]
normal_reload_time = 1.0
reload_start_time = 0.4
reload_bullet_time = 0.2
projectile_scene = ExtResource("48_8kt16")
shot_raycast = NodePath("../Interpolated Camera/Arm/Arm Anchor/Camera/Gun Raycast")
animation_player = NodePath("../Interpolated Camera/Arm/Arm Anchor/Camera/v_revolver/AnimationPlayer")
ammo_indicator_path = NodePath("../HUD/Control/Center/AmmoIndicator")
shot_sound = ExtResource("49_8kt16")
bigshot_sound = ExtResource("50_8kt16")
impact_particle_scene = ExtResource("50_fa17f")
bullet_trail_scene = ExtResource("51_4y3xk")
muzzle_flash_scene = ExtResource("52_5mmtj")
bullet_hole_scene = ExtResource("53_vijo2")
gun_start_marker = NodePath("../Interpolated Camera/Arm/Arm Anchor/Camera/v_revolver/gunframe_001/GunStart")

[node name="HookController" parent="." node_paths=PackedStringArray("hook_spawn_marker", "player_body", "crosshair_tex", "charge_indicator") instance=ExtResource("48_icefa")]
kickboost_vertical = 3.0
kickboost_charge_refill_time = 1.4
hook_spawn_marker = NodePath("../Interpolated Camera/Arm/Arm Anchor/Camera/v_revolver/v_hook_arm/HookRopeStart")
pull_speed = 65.0
player_body = NodePath("../Body")
crosshair_tex = NodePath("../HUD/Control/Center/crosshairTex")
charge_indicator = NodePath("../HUD/Control/Center/ChargeIndicator")
hook_hit_sound = ExtResource("25_ejsji")
hook_retract_sound = ExtResource("50_321qo")

[editable path="HUD/Control/Center/AmmoIndicator"]
[editable path="Interpolated Camera/Arm/Arm Anchor/Camera/v_revolver"]
