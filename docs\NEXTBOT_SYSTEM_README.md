# NextBot System for Godot 4.4

A comprehensive port of the Source SDK NextBot AI system to Godot 4.4, maintaining the original architecture while leveraging Godot's built-in systems.

## Overview

This NextBot implementation provides a sophisticated, component-based AI framework that mirrors the Source SDK's NextBot system. It features:

- **Component-based architecture** with Locomotion, Body, Vision, and Intention interfaces
- **Hierarchical action system** for complex behavior trees
- **Event-driven communication** between components
- **Advanced pathfinding** using Godot's NavigationServer3D
- **Comprehensive debugging tools** and visualization
- **Legacy compatibility** with existing bot systems

## Quick Setup

### 1. Add NextBot Manager as Autoload

1. Go to **Project → Project Settings → Autoload**
2. Add `scripts/enemy/nextbot_manager.gd` with name `NextBotManager`
3. Enable it as a singleton

### 2. Create a NextBot Scene

Create a new scene with this structure:
```
EnhancedNextBot (EnhancedNextBot script)
├── NavigationAgent3D
├── CollisionShape3D
├── MeshInstance3D
├── AnimationPlayer
└── Components/
    ├── EnhancedLocomotionComponent (EnhancedLocomotionComponent script)
    ├── EnhancedBodyComponent (EnhancedBodyComponent script)
    ├── EnhancedVisionComponent (EnhancedVisionComponent script)
    ├── EnhancedIntentionComponent (EnhancedIntentionComponent script)
    ├── LocomotionComponent (LocomotionComponent script) [Legacy compatibility]
    ├── BodyComponent (BodyComponent script) [Legacy compatibility]
    ├── VisionComponent (VisionComponent script) [Legacy compatibility]
    └── IntentionComponent (IntentionComponent script) [Legacy compatibility]
```

### 3. Configure Navigation

1. Add a NavigationRegion3D to your scene
2. Set up navigation mesh for your level
3. Ensure your bot's NavigationAgent3D is properly configured

## Architecture Overview

### Core Components

#### ILocomotion (Locomotion Interface)
- **Purpose**: Handles all movement and pathfinding
- **Key Methods**: `approach()`, `drive_to()`, `jump()`, `climb_up_to_ledge()`
- **Features**: Stuck detection, path following, collision avoidance

#### IBody (Body Interface)
- **Purpose**: Manages physical representation and animations
- **Key Methods**: `start_activity()`, `aim_head_towards()`, `get_eye_position()`
- **Features**: Posture control, animation management, hull properties

#### IVision (Vision Interface)
- **Purpose**: Handles perception and entity tracking
- **Key Methods**: `is_able_to_see()`, `get_primary_known_threat()`, `collect_known_entities()`
- **Features**: Line-of-sight calculations, threat assessment, memory system

#### IIntention (Intention Interface)
- **Purpose**: Controls high-level decision making
- **Key Methods**: `get_initial_action()`, `select_more_dangerous_threat()`
- **Features**: Action management, tactical decision making

### Action System

Actions are hierarchical behaviors that can:
- **Start, Update, End**: Standard lifecycle methods
- **Suspend/Resume**: Handle interruptions
- **Query**: Provide decision-making information
- **Handle Events**: Respond to game world changes

#### Available Actions

- **IdleAction**: Basic idle behavior with threat detection
- **PatrolAction**: Move between waypoints or patrol areas
- **SeekAndDestroyAction**: Hunt down and engage enemies
- **AttackAction**: Close combat engagement
- **RetreatAction**: Flee from threats to safety
- **InvestigateAction**: Investigate sounds or suspicious activity
- **UnstuckAction**: Attempt to resolve stuck situations
- **DeathAction**: Handle death state and respawn

### Event System

The system uses a comprehensive event framework:

#### Movement Events
- `on_leave_ground()`, `on_land_on_ground()`
- `on_move_to_success()`, `on_move_to_failure()`
- `on_stuck()`, `on_unstuck()`

#### Perception Events
- `on_sight()`, `on_lost_sight()`
- `on_sound()`, `on_weapon_fired()`

#### Combat Events
- `on_injured()`, `on_killed()`
- `on_other_killed()`

## Usage Examples

### Basic Bot Creation

```gdscript
# Create a simple bot that patrols and engages threats
extends EnhancedNextBot

func _ready():
    super._ready()
    
    # Set bot properties
    health = 100.0
    team = 1
    debug_name = "PatrolBot"
    
    # Start with patrol behavior
    var intention = get_intention_interface()
    if intention:
        intention.change_action(PatrolAction.new())
```

### Custom Action Creation

```gdscript
# Create a custom guard action
class_name GuardAction
extends NextBotAction

var guard_position: Vector3
var guard_radius: float = 5.0

func _init(pos: Vector3):
    action_name = "Guard"
    guard_position = pos

func on_start(bot: INextBot, prior_action: NextBotAction) -> ActionResult:
    super.on_start(bot, prior_action)
    
    # Move to guard position
    var locomotion = bot.get_locomotion_interface()
    if locomotion:
        locomotion.approach(guard_position)
    
    return ActionResult.continue_action()

func update(bot: INextBot, delta: float) -> ActionResult:
    # Check for threats
    var vision = bot.get_vision_interface()
    if vision:
        var threat = vision.get_primary_known_threat(true)
        if threat:
            return ActionResult.change_to(AttackAction.new(), "threat detected")
    
    # Stay near guard position
    var distance = bot.get_position().distance_to(guard_position)
    if distance > guard_radius:
        var locomotion = bot.get_locomotion_interface()
        if locomotion:
            locomotion.approach(guard_position)
    
    return ActionResult.continue_action()
```

### Event Handling

```gdscript
# Custom bot with enhanced event handling
extends EnhancedNextBot

func on_injured(damage_info: Dictionary) -> void:
    super.on_injured(damage_info)
    
    # Custom injury response
    var attacker = damage_info.get("attacker")
    if attacker:
        print("Bot injured by: ", attacker.name)
        
        # Become more aggressive
        var intention = get_intention_interface()
        if intention:
            intention.change_action(SeekAndDestroyAction.new())

func on_sight(entity: Node) -> void:
    super.on_sight(entity)
    
    # Custom sight response
    if entity.has_method("get_team") and entity.get_team() != team:
        print("Enemy spotted: ", entity.name)
```

## Debugging

### Debug Commands

The system includes comprehensive debugging tools:

```gdscript
# Enable debug visualization
NextBotManager.set_debug_types(NextBotManager.DebugType.ALL)

# Filter debug output to specific bots
NextBotManager.debug_filter_add("MyBot")

# Select a bot for detailed debugging
NextBotManager.select_bot(my_bot)
```

### Debug Hotkeys (in debug builds)

- **Ctrl+F1**: Toggle behavior debugging
- **Ctrl+F2**: Toggle path debugging  
- **Ctrl+F3**: Toggle vision debugging

### Debug Visualization

When enabled, the system shows:
- **Path lines** showing bot movement paths
- **Vision cones** showing field of view
- **Threat indicators** showing known entities
- **Action states** showing current behaviors

## Performance Considerations

### Update Optimization

The NextBot Manager automatically optimizes updates:
- **Throttled updates**: Only updates a subset of bots per frame
- **Distance culling**: Can disable distant bots
- **Component scheduling**: Spreads component updates across frames

### Memory Management

- **Entity cleanup**: Automatically removes invalid entities
- **Path caching**: Reuses computed paths when possible
- **Event pooling**: Minimizes garbage collection

## Migration from Legacy Systems

The system provides compatibility layers for existing bot code:

### Legacy Method Support

```gdscript
# Old methods still work
bot.move_to(target_position)
bot.face_toward(target_position)
bot.can_see(target)
bot.has_threats()
```

### Gradual Migration

You can migrate incrementally:
1. Start with legacy components
2. Replace one component at a time with enhanced versions
3. Gradually adopt the action system
4. Remove legacy components when ready

## Extending the System

### Custom Components

```gdscript
# Create custom component
class_name CustomComponent
extends INextBotComponent

func on_update(delta: float) -> void:
    # Custom component logic
    pass
```

### Custom Interfaces

```gdscript
# Extend existing interfaces
class_name EnhancedVision
extends IVision

func detect_stealth_enemies() -> Array:
    # Custom vision enhancement
    pass
```

## Troubleshooting

### Common Issues

1. **Bot not moving**: Check NavigationAgent3D setup and navigation mesh
2. **Actions not working**: Ensure NextBotManager is autoloaded
3. **Events not firing**: Verify component initialization
4. **Performance issues**: Enable update throttling and distance culling

### Debug Output

Enable verbose logging:
```gdscript
NextBotManager.set_debug_types(NextBotManager.DebugType.ALL)
```

## Contributing

When extending the system:
1. Follow the existing architecture patterns
2. Maintain backward compatibility
3. Add comprehensive documentation
4. Include debug visualization
5. Test with multiple bot scenarios

## License

This NextBot implementation is based on the Source SDK architecture and is provided for educational and development purposes.
