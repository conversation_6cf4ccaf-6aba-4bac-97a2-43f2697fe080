# Health System Implementation

## Overview

The health system has been updated to implement a 2 HP system with invulnerability and healing mechanics as requested.

## Key Features

### 2 HP System
- Player starts with 2 HP maximum
- Each hit reduces health by 1 HP
- At 0 HP, player dies

### Invulnerability System
- **1.3 seconds of invulnerability** after taking damage
- During invulnerability, additional damage is completely blocked
- Invulnerability timer is reset each time damage is taken

### Healing System
- **12-second delay** before healing starts when not taking damage
- If player takes damage during the 12-second wait, the timer resets
- Healing occurs gradually (1 HP every 0.1 seconds for smooth effect)
- Healing stops when player reaches full health

### UI Integration
- HP UI shows **hurt graphic** when at 1 HP
- HP UI shows **full health graphic** when at 2 HP
- HP UI shows **dead graphic** when at 0 HP
- UI shakes when player takes damage

## Implementation Details

### Health Component (`resource/entities/player/player_health.gd`)
- Handles all health logic including invulnerability and healing
- Emits signals for damage, healing, and invulnerability states
- Uses `_process()` to handle timers for invulnerability and healing
- Blocks damage when invulnerable

### Player State (`resource/entities/player/player_state.gd`)
- Updated to work with the new health component
- Removed internal hit counter (health component handles everything)
- Still emits `hit_taken` signal for UI compatibility
- Handles stunned state when taking damage

### HP UI (`scenes/ui/hp.gd`)
- Updated to properly show hurt graphic at 1 HP
- Shakes when player takes damage
- Shows appropriate graphics based on health state

## Testing

A test script has been created (`scripts/health_test.gd`) that can be attached to any node to test the health system. The script responds to these input actions:

- `test_damage` - Test taking damage
- `test_heal` - Test healing
- `test_invulnerability` - Test invulnerability (damage should be blocked)
- `test_healing_timer` - Test the 12-second healing delay

## Configuration

The health system can be configured through these exported variables in the health component:

- `max_health: int = 2` - Maximum health (should stay at 2)
- `start_health: int = 2` - Starting health
- `invulnerability_duration: float = 1.3` - Seconds of invulnerability
- `healing_delay: float = 12.0` - Seconds before healing starts

## Signals

The health component emits these signals:

- `health_changed(current_health, max_health)` - When health changes
- `damage_taken(amount, current_health, max_health)` - When damage is taken
- `died` - When player dies
- `invulnerability_started` - When invulnerability begins
- `invulnerability_ended` - When invulnerability ends
- `healing_started` - When healing begins
- `healing_completed` - When healing finishes

## Usage

To use this system:

1. Ensure the player has a health component attached
2. The player state will automatically connect to the health component
3. The HP UI will automatically connect to both health component and player state
4. Call `take_hit(1)` on the player state to damage the player
5. The system will handle invulnerability and healing automatically

The system is now ready for testing and should behave exactly as specified in the requirements. 