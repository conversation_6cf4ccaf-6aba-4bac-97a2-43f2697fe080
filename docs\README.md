# Stiletto Proto Documentation

Welcome to the Stiletto Proto documentation. This folder contains comprehensive guides, specifications, and implementation details for the project.

## Quick Navigation

### 🎮 Core Systems
- **[ENEMY_SYSTEM_GUIDE.md](ENEMY_SYSTEM_GUIDE.md)** - Complete enemy system overview
- **[WAVE_POOL_SYSTEM.md](WAVE_POOL_SYSTEM.md)** - Custom enemy spawn configuration
- **[HEALTH_SYSTEM_IMPLEMENTATION.md](HEALTH_SYSTEM_IMPLEMENTATION.md)** - Health and damage system
- **[MUSIC_SYSTEM_GUIDE.md](MUSIC_SYSTEM_GUIDE.md)** - Dynamic music system

### 🤖 AI & Behavior
- **[NEXTBOT_SYSTEM_README.md](NEXTBOT_SYSTEM_README.md)** - NextBot AI framework
- **[nextbot_ai_analysis.md](nextbot_ai_analysis.md)** - Deep dive into AI behavior
- **[ai_system_example.md](ai_system_example.md)** - AI system examples
- **[ULTRA_LIGHT_KNIGHT_SYSTEM.md](ULTRA_LIGHT_KNIGHT_SYSTEM.md)** - Optimized knight AI
- **[KNIGHT_AI_OPTIMIZATION_SUMMARY.md](KNIGHT_AI_OPTIMIZATION_SUMMARY.md)** - Performance optimizations

### 🌐 Multiplayer
- **[MULTIPLAYER_FRAMEWORK_SPECIFICATIONS.md](MULTIPLAYER_FRAMEWORK_SPECIFICATIONS.md)** - Multiplayer architecture
- **[IMPLEMENTATION_ROADMAP.md](IMPLEMENTATION_ROADMAP.md)** - Development roadmap

### 🛠️ Development & Setup
- **[SETUP_GUIDE.md](SETUP_GUIDE.md)** - Project setup instructions
- **[CRITICAL_SETUP_FIX.md](CRITICAL_SETUP_FIX.md)** - Critical setup fixes
- **[navigation_comparison.md](navigation_comparison.md)** - Navigation system comparison
- **[source_inspired_example.md](source_inspired_example.md)** - Source engine inspiration

### 🔧 Technical Fixes
- **[ARCHER_FIXES.md](ARCHER_FIXES.md)** - Archer enemy fixes
- **[GET_TREE_FIX.md](GET_TREE_FIX.md)** - Tree access fixes
- **[COROUTINE_AWAIT_FINAL_FIX.md](COROUTINE_AWAIT_FINAL_FIX.md)** - Coroutine fixes
- **[COROUTINE_FIX.md](COROUTINE_FIX.md)** - Additional coroutine fixes
- **[BOT_SCOPE_FIX.md](BOT_SCOPE_FIX.md)** - Bot scope issues
- **[ACTION_PARAMETER_FIX.md](ACTION_PARAMETER_FIX.md)** - Action parameter fixes
- **[GET_POSITION_FIX.md](GET_POSITION_FIX.md)** - Position access fixes
- **[AUTOLOAD_PARSING_FIX.md](AUTOLOAD_PARSING_FIX.md)** - Autoload parsing fixes

### 📚 Reference
- **[simple_vs_complex.md](simple_vs_complex.md)** - System complexity comparison
- **[LICENSE.md](LICENSE.md)** - Project license

## Getting Started

1. **New to the project?** Start with [SETUP_GUIDE.md](SETUP_GUIDE.md)
2. **Working with enemies?** Read [ENEMY_SYSTEM_GUIDE.md](ENEMY_SYSTEM_GUIDE.md)
3. **Configuring spawns?** Check [WAVE_POOL_SYSTEM.md](WAVE_POOL_SYSTEM.md)
4. **Adding AI?** Review [NEXTBOT_SYSTEM_README.md](NEXTBOT_SYSTEM_README.md)

## Documentation Standards

- **Code examples** are provided in GDScript
- **File paths** are relative to the project root
- **Cross-references** link to related documentation
- **Troubleshooting** sections address common issues

## Contributing

When adding new documentation:
1. Use clear, descriptive filenames
2. Include code examples where relevant
3. Cross-reference related documents
4. Add entries to this README index

## Project Structure

```
stiletto-proto/
├── docs/                    # This documentation folder
├── resource/               # Game resources and scripts
├── scenes/                 # Scene files
├── maps/                   # Level files
├── assets/                 # Art and audio assets
└── scripts/                # Additional scripts
```

For detailed project structure, see individual system guides.
