# Wave Pool System Documentation

## Overview

The Wave Pool System allows you to configure custom enemy compositions for spawners in survival maps. Instead of using the default knight/archer logic, you can define weighted pools of enemy types that spawners will randomly select from.

## Architecture

### Core Components

1. **EnemyPoolEntry** (`resource/entities/EnemyPoolEntry.gd`)
   - Individual enemy entry in a wave pool
   - Contains a scene reference and spawn weight

2. **EnemyWavePool** (`resource/entities/EnemyWavePool.gd`)
   - Collection of enemy entries with weighted selection logic
   - Handles random enemy selection based on weights

3. **EnemySpawner** (`resource/entities/enemy_spawner.gd`)
   - Modified to accept and use wave pools
   - Falls back to default behavior when no pool is provided

4. **GameMaster** (`resource/scripts/gamemaster_enhanced.gd`)
   - Can assign a default wave pool to all spawners it creates

## Usage Guide

### Creating a Wave Pool

1. **Create the Resource**
   - In the FileSystem panel, right-click → New Resource
   - Select "EnemyWavePool" from the class list
   - Save it as `.tres` file (e.g., `my_wave_pool.tres`)

2. **Add Enemy Entries**
   - In the Inspector, expand the "Entries" array
   - Add new elements (each becomes an EnemyPoolEntry)
   - For each entry:
     - **Scene**: Drag your enemy scene (e.g., `knight.tscn`, `archer.tscn`)
     - **Weight**: Set spawn probability (higher = more likely to spawn)

### Example Wave Pool Configuration

```
Wave Pool: "Mixed_Combat.tres"
├── Entry 1: Knight Scene (weight: 0.7)
├── Entry 2: Archer Scene (weight: 0.3)
└── Entry 3: Charger Scene (weight: 0.1)
```

This would spawn approximately:
- 70% Knights
- 30% Archers  
- 10% Chargers (relative to total weight)

### Applying Wave Pools

#### Method 1: Map-Level Default (Recommended)
1. Open your map scene
2. Select the GameMaster node
3. In Inspector, set "Default Wave Pool" to your wave pool resource
4. All spawners created by GameMaster will use this pool

#### Method 2: Per-Spawner Override
1. Open the spawner scene (`resource/entities/enemy_spawner.tscn`)
2. Select the spawner node
3. In Inspector, set "Wave Pool" to your custom pool
4. This spawner will use your pool instead of the default

#### Method 3: Runtime Assignment
```gdscript
# In code, assign wave pool to a spawner
var spawner = enemy_spawner_scene.instantiate()
spawner.wave_pool = my_wave_pool_resource
```

## Weight System

### How Weights Work
- Weights are relative probabilities
- Higher weight = higher chance of selection
- Weights are summed, then random selection occurs
- Zero or negative weights are treated as 0

### Weight Examples

**Equal Distribution:**
```
Knight: weight 1.0
Archer: weight 1.0
Result: 50% each
```

**Knight-Heavy:**
```
Knight: weight 3.0
Archer: weight 1.0
Result: 75% Knight, 25% Archer
```

**Rare Special Enemy:**
```
Knight: weight 8.0
Archer: weight 2.0
Charger: weight 0.5
Result: ~76% Knight, ~19% Archer, ~5% Charger
```

## Fallback Behavior

When no wave pool is assigned:
1. Spawner checks for `wave_pool` property
2. If null or empty, uses original knight/archer logic:
   - `force_knights_only` → spawns only knights
   - `archer_only_area` → spawns only archers
   - `mixed_knight_ratio` → uses ratio for knight/archer mix

## Advanced Usage

### Dynamic Wave Pools
You can modify wave pools at runtime:

```gdscript
# Change weights based on game state
if player_health < 50:
    wave_pool.entries[0].weight = 2.0  # More knights when player is weak
else:
    wave_pool.entries[0].weight = 0.5  # Fewer knights when player is strong
```

### Wave-Specific Pools
Create different pools for different waves:

```gdscript
# In GameMaster spawner creation
if _wave <= 2:
    spawner_instance.wave_pool = early_wave_pool
elif _wave <= 5:
    spawner_instance.wave_pool = mid_wave_pool
else:
    spawner_instance.wave_pool = late_wave_pool
```

### Conditional Pool Selection
```gdscript
# Select pool based on spawn area
if area.is_in_group("difficult_zone"):
    spawner_instance.wave_pool = hard_pool
elif area.is_in_group("easy_zone"):
    spawner_instance.wave_pool = easy_pool
```

## Best Practices

### Pool Design
1. **Keep pools focused** - Don't mix too many enemy types
2. **Use meaningful weights** - Avoid extreme values (0.001 or 1000)
3. **Test thoroughly** - Verify spawn rates match expectations
4. **Document pools** - Name them clearly and add comments

### Performance
1. **Reuse pools** - Create pools once, assign to multiple spawners
2. **Limit pool size** - Don't create pools with 50+ entries
3. **Cache references** - Store pool references in variables

### Balance
1. **Start simple** - Begin with 2-3 enemy types
2. **Iterate gradually** - Small weight changes, test, repeat
3. **Consider difficulty** - Harder enemies should have lower weights
4. **Match map theme** - Use appropriate enemies for the environment

## Troubleshooting

### Common Issues

**No enemies spawning:**
- Check if wave pool is empty
- Verify scene references are valid
- Ensure weights are positive numbers

**Wrong enemy types:**
- Confirm wave pool is assigned to spawner
- Check scene paths in pool entries
- Verify fallback logic isn't overriding

**Unexpected spawn rates:**
- Recalculate weight ratios
- Check for zero/negative weights
- Test with simple equal-weight pools

### Debug Tips

```gdscript
# Add debug output to spawner
func _choose_enemy_scene() -> PackedScene:
    if wave_pool:
        print("Using wave pool with ", wave_pool.entries.size(), " entries")
        var scene = wave_pool.pick_scene(_rng)
        print("Selected scene: ", scene.resource_path if scene else "null")
        return scene
    else:
        print("No wave pool, using fallback logic")
        # ... existing logic
```

## File Structure

```
resource/entities/
├── EnemyPoolEntry.gd      # Individual enemy entry
├── EnemyWavePool.gd       # Pool collection and selection
└── enemy_spawner.gd       # Modified spawner with pool support

resource/scripts/
└── gamemaster_enhanced.gd # GameMaster with default pool support

docs/
└── WAVE_POOL_SYSTEM.md   # This documentation
```

## Migration from Old System

If you have existing spawners using the old knight/archer system:

1. **No changes needed** - Old spawners continue working
2. **Gradual migration** - Assign wave pools to specific spawners
3. **Test thoroughly** - Ensure new pools work as expected
4. **Update GameMaster** - Set default pool for new spawners

The wave pool system is designed to be backward-compatible with existing spawner configurations. 